#!/usr/bin/env node

/**
 * H5 Vue 项目 Node.js 部署脚本 (CommonJS)
 * 使用方法: node deploy.cjs [环境]
 * 环境选项: dev, pre, prod (默认: prod)
 */

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

// 配置
const CONFIG = {
  server: {
    host: "**************",
    user: "luyi",
    path: "/usr/share/nginx/demo",
  },
  environments: ["dev", "test", "pre", "prod"],
  buildCommands: {
    dev: "npm run build:dev",
    test: "npm run build:test",
    pre: "npm run build:pre",
    prod: "npm run build:prod",
  },
};

// 颜色输出
const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  cyan: "\x1b[36m",
  magenta: "\x1b[35m",
  white: "\x1b[37m",
  // 背景色
  bgRed: "\x1b[41m",
  bgGreen: "\x1b[42m",
  bgYellow: "\x1b[43m",
  bgBlue: "\x1b[44m",
  // 样式
  bold: "\x1b[1m",
  dim: "\x1b[2m",
  underline: "\x1b[4m",
};

function log(message, color = "reset") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, "red");
}

function logSuccess(message) {
  log(`✅ ${message}`, "green");
}

function logInfo(message) {
  log(`ℹ️  ${message}`, "blue");
}

function logWarning(message) {
  log(`⚠️  ${message}`, "yellow");
}

function logHighlight(message) {
  log(`${colors.bold}${colors.cyan}${message}${colors.reset}`, "reset");
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const size = (bytes / Math.pow(k, i)).toFixed(1);
  return `${size} ${sizes[i]}`;
}

// 获取文件大小（字节）
function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return stats.size;
  } catch {
    return 0;
  }
}

// 获取目录大小（字节）
function getDirSize(dirPath) {
  let totalSize = 0;

  function calculateSize(currentPath) {
    const stats = fs.statSync(currentPath);
    if (stats.isFile()) {
      totalSize += stats.size;
    } else if (stats.isDirectory()) {
      const files = fs.readdirSync(currentPath);
      files.forEach((file) => {
        calculateSize(path.join(currentPath, file));
      });
    }
  }

  try {
    calculateSize(dirPath);
    return totalSize;
  } catch {
    return 0;
  }
}

// 高亮显示大文件
function highlightLargeFiles(size, filename) {
  const sizeInMB = size / (1024 * 1024);

  if (sizeInMB > 5) {
    // 超过5MB的文件用红色高亮
    return `${colors.bold}${colors.red}${formatFileSize(size)}${colors.reset} ${
      colors.red
    }${filename}${colors.reset}`;
  } else if (sizeInMB > 1) {
    // 超过1MB的文件用黄色高亮
    return `${colors.bold}${colors.yellow}${formatFileSize(size)}${colors.reset} ${
      colors.yellow
    }${filename}${colors.reset}`;
  } else if (sizeInMB > 0.5) {
    // 超过500KB的文件用蓝色高亮
    return `${colors.bold}${colors.blue}${formatFileSize(size)}${colors.reset} ${filename}`;
  } else {
    // 小文件正常显示
    return `${colors.dim}${formatFileSize(size)}${colors.reset} ${filename}`;
  }
}

// 执行命令
function execCommand(command, options = {}) {
  try {
    const result = execSync(command, {
      encoding: "utf8",
      stdio: options.silent ? "pipe" : "inherit",
      ...options,
    });
    return { success: true, output: result };
  } catch (error) {
    return { success: false, error: error.message, output: error.stdout };
  }
}

// 检查命令是否存在
function commandExists(command) {
  try {
    execSync(`which ${command}`, { stdio: "pipe" });
    return true;
  } catch {
    return false;
  }
}

// 快速检查依赖
function checkDependencies() {
  const dependencies = ["node", "rsync"];
  const missing = dependencies.filter((dep) => !commandExists(dep));

  if (missing.length > 0) {
    logError(`缺少依赖: ${missing.join(", ")}`);
    if (missing.includes("rsync")) {
      logInfo("安装 rsync: brew install rsync (macOS) 或 apt-get install rsync (Ubuntu)");
    }
    process.exit(1);
  }
}

// 智能检查依赖
function checkNodeModules() {
  if (!fs.existsSync("node_modules") || !fs.existsSync("node_modules/.pnpm")) {
    logInfo("安装项目依赖...");
    const result = execCommand("pnpm install --frozen-lockfile");
    if (!result.success) {
      logError("依赖安装失败");
      process.exit(1);
    }
    logSuccess("依赖安装完成");
  }
}

// 构建项目
function buildProject(environment) {
  logInfo(`构建项目 (${environment} 环境)...`);

  const buildCommand = CONFIG.buildCommands[environment];
  if (!buildCommand) {
    logError(`未知环境: ${environment}`);
    process.exit(1);
  }

  const result = execCommand(buildCommand);
  if (!result.success) {
    logError("项目构建失败");
    process.exit(1);
  }

  logSuccess("项目构建完成");
}

// 分析并显示构建结果
function checkBuildResult() {
  if (!fs.existsSync("dist")) {
    logError("构建失败，dist 目录不存在");
    process.exit(1);
  }

  const files = fs.readdirSync("dist");
  if (files.length === 0) {
    logError("构建失败，dist 目录为空");
    process.exit(1);
  }

  // 获取总大小
  const totalSize = getDirSize("dist");

  logSuccess(`构建完成 - ${files.length} 个文件`);
  logHighlight(`📊 总大小: ${formatFileSize(totalSize)}`);

  // 分析文件结构
  analyzeDistFiles();
}

// 分析 dist 目录文件
function analyzeDistFiles() {
  console.log("");
  logHighlight("📁 构建文件分析:");
  console.log("".padEnd(50, "="));

  const distPath = "dist";
  const filesByType = {
    js: [],
    css: [],
    images: [],
    fonts: [],
    other: [],
  };

  // 递归收集所有文件
  function collectFiles(dir, relativePath = "") {
    const items = fs.readdirSync(dir);

    items.forEach((item) => {
      const fullPath = path.join(dir, item);
      const relativeFilePath = path.join(relativePath, item);
      const stats = fs.statSync(fullPath);

      if (stats.isDirectory()) {
        collectFiles(fullPath, relativeFilePath);
      } else {
        const ext = path.extname(item).toLowerCase();
        const size = stats.size;
        const fileInfo = { name: relativeFilePath, size, fullPath };

        if (ext === ".js") {
          filesByType.js.push(fileInfo);
        } else if (ext === ".css") {
          filesByType.css.push(fileInfo);
        } else if ([".png", ".jpg", ".jpeg", ".gif", ".svg", ".webp", ".ico"].includes(ext)) {
          filesByType.images.push(fileInfo);
        } else if ([".woff", ".woff2", ".ttf", ".eot"].includes(ext)) {
          filesByType.fonts.push(fileInfo);
        } else {
          filesByType.other.push(fileInfo);
        }
      }
    });
  }

  collectFiles(distPath);

  // 显示各类型文件统计
  displayFileTypeStats(filesByType);

  // 显示最大的文件
  displayLargestFiles(filesByType);

  console.log("".padEnd(50, "="));
}

// 显示文件类型统计
function displayFileTypeStats(filesByType) {
  const stats = Object.entries(filesByType)
    .map(([type, files]) => {
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);
      const count = files.length;
      return { type, count, totalSize, files };
    })
    .filter((stat) => stat.count > 0);

  stats.forEach(({ type, count, totalSize }) => {
    const typeIcon =
      {
        js: "📜",
        css: "🎨",
        images: "🖼️",
        fonts: "🔤",
        other: "📄",
      }[type] || "📄";

    console.log(
      `${typeIcon} ${type.toUpperCase().padEnd(8)} ${count
        .toString()
        .padStart(3)} 个文件  ${formatFileSize(totalSize).padStart(10)}`
    );
  });

  console.log("");
}

// 显示最大的文件（前10个）
function displayLargestFiles(filesByType) {
  const allFiles = Object.values(filesByType).flat();
  const largestFiles = allFiles.sort((a, b) => b.size - a.size).slice(0, 10);

  if (largestFiles.length > 0) {
    logHighlight("🔍 最大的文件 (前10个):");
    largestFiles.forEach((file, index) => {
      const rank = `${index + 1}.`.padStart(3);
      const sizeStr = formatFileSize(file.size).padStart(10);
      const highlighted = highlightLargeFiles(file.size, file.name);
      console.log(`${rank} ${sizeStr} ${highlighted}`);
    });
    console.log("");
  }
}

// 部署到服务器
function deployToServer() {
  logInfo("上传文件到服务器...");

  // 显示上传信息
  console.log(`📤 目标服务器: ${CONFIG.server.user}@${CONFIG.server.host}`);
  console.log(`📁 目标路径: ${CONFIG.server.path}/`);
  console.log(`📦 本地路径: dist/`);
  console.log("");

  // 直接使用 rsync 同步文件，rsync 会自动创建目标目录
  // 移除单独的 mkdir 命令，避免多次 SSH 连接
  const rsyncCommand = `rsync -azvp dist/* luyi@**************:/usr/share/nginx/demo/.`;

  logInfo("🚀 开始同步文件...");
  logInfo(`📡 目标服务器: ${serverConfig.user}@${serverConfig.host}:${serverConfig.path}`);
  logWarning("⚠️  可能需要输入服务器密码");
  console.log("");

  try {
    // 直接使用 execSync 而不是 execCommand 包装器
    // 这样可以更好地处理交互式输入
    execSync(rsyncCommand, {
      stdio: "inherit",
      encoding: "utf8",
    });

    console.log("");
    logSuccess("✅ 文件上传成功");
  } catch (error) {
    console.log("");
    logError("文件上传失败");

    // 分析具体错误原因
    if (error.message.includes("Permission denied")) {
      logError("❌ 权限被拒绝 - 请检查用户名和密码");
    } else if (error.message.includes("Connection refused")) {
      logError("❌ 连接被拒绝 - 请检查服务器地址和SSH服务");
    } else if (error.message.includes("Host key verification failed")) {
      logError("❌ 主机密钥验证失败");
      logInfo("🔧 解决方法: ssh-keygen -R *************");
    } else {
      logError(`错误详情: ${error.message}`);
    }

    logWarning("💡 提示：如需避免输入密码，请配置 SSH 密钥认证");
    logInfo("📝 配置方法：运行 ./setup-ssh.sh 脚本");
    process.exit(1);
  }
}

// 主函数
function main() {
  const environment = process.argv[2] || "pre";

  // 验证环境参数
  if (!CONFIG.environments.includes(environment)) {
    logError(`无效的环境参数: ${environment}`);
    logInfo(`使用方法: node deploy.cjs [${CONFIG.environments.join("|")}]`);
    process.exit(1);
  }

  const isQuickMode = process.argv.includes("--quick") || process.argv.includes("-q");

  log(`🚀 ${isQuickMode ? "快速" : ""}部署到 ${environment} 环境...`, "blue");
  if (isQuickMode) {
    logInfo("⚡ 快速模式：跳过部分检查");
  }

  try {
    const startTime = Date.now();

    // 1. 快速检查依赖
    if (!isQuickMode) {
      checkDependencies();
      checkNodeModules();
    }

    // 2. 构建项目
    buildProject(environment);

    // 3. 检查构建结果
    checkBuildResult();

    // 4. 部署到服务器
    deployToServer();

    // 5. 完成
    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    console.log("");
    logSuccess(`🎉 部署完成！耗时 ${duration}s`);
    logInfo(`📍 ${CONFIG.server.user}@${CONFIG.server.host}:${CONFIG.server.path}`);
  } catch (error) {
    logError(`部署失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main, CONFIG };
