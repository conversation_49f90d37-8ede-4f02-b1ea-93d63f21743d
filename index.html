<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <link />
    <title>NuStar</title>
    <!-- 客服库 -->
    <script src="https://cdn.aihelp.net/webchatv5/aihelp.js" async></script>
    <!-- Geetest 验证码库 - 异步加载，不阻塞页面 -->
    <script src="https://static.geetest.com/g5/gd.js" async></script>
    <script src="https://static.geetest.com/v4/gt4.js" async></script>
    <script>
      var coverSupport =
        "CSS" in window &&
        typeof CSS.supports === "function" &&
        (CSS.supports("top: env(a)") || CSS.supports("top: constant(a)"));
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
          (coverSupport ? ", viewport-fit=cover" : "") +
          '" />'
      );
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
