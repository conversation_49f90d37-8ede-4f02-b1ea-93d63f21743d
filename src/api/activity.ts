import http from "@/utils/http";

export const getRankCasino = (data) => {
  return http.post("/avt/api/rank/casino", data, {
    type: "formData",
  });
};

export const getRankjili = (data = {}) => {
  return http.post("/avt/api/rank/slot", data, { type: "formData" });
};

// 获取弹窗奖励列表
export const getActivityBonusList = (data = {}) => {
  return http.post("/avt/api/activity/bonus_list", data, { type: "formData" });
};
// 获取弹窗奖励列表
export const getAdjustmentBonusRecordList = (params) => {
  return http.get("/activity/adjustment_bonus_record/list", {
    params,
  });
};
/**活动调账枚举数据*/
export const getAdjustmentList = (params = {}) => {
  return http.get("/avt/api/activity/adjustment/enum", { params });
};
// 领取弹窗活动奖励
export const postActivityBonusReceive = (data = {}) => {
  return http.post("/avt/api/activity/bonus_receive", data, { type: "formData" });
};
//新加的 手动领取奖励 通知服务器
export const postAdjustmentBonusRecordReceive = (data = {}) => {
  return http.post("/open/api/activity/adjustment_bonus_record/receive", data, {
    type: "formData",
  });
};

// 获取转盘活动是否开启以及剩余抽奖次数
export const getActivitySpinConfig = (params = {}) => {
  return http.get("/open/api/activity/spin/config", {
    params: { ...params, action: null },
    transformResult: (res) => res.data,
  });
};
// 获取转盘活动配置以及领取信息
export const getActivitySpinReceiveInfo = (params = {}) => {
  return http.get("/open/api/activity/spin/config/receive_info", {
    params,
    transformResult: (res) => res.data,
  });
};
// 个人获取转盘活动领奖记录  - 废弃
export const getActivitySpinConfigReceiveRecord = (params = {}) => {
  return http.get("/open/api/activity/spin/config/receive_record", { params });
};
// 平台转盘活动领奖记录
export const getActivitySpinConfigReceiveRecordPlatform = (params = {}) => {
  return http.get("/open/api/activity/spin/config/receive_record/platform", {
    params,
    transformResult: (res) => res.data,
  });
};
//  获取转盘结果
export const spinResult = (data = {}) => {
  return http.post(
    "/activity/spin/prize",
    { ...data, action: undefined },
    {
      transformResult: (res) => res.data,
    }
  );
};
