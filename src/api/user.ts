import { ALL_APP_SOURCE_CONFIG } from "../utils/config/Config";
import http from "@/utils/http";

export interface LoginParams {
  username: string;
  password: string;
}
export interface LoginData {
  login_type: string;
  session_id: string;
  token: string;
  gcash_auth_code: string;

  appChannel: string;
  appPackageName: string;
  deviceId: string;
  deviceModel: string;
  deviceVersion: string;
  appVersion: string;
  sysTimezone: string;
  sysLanguage: string;
  source: String | Number;
  telephoneCode: string;
  isNative: string;
  registration_channel: string;
}

export interface LoginResult {
  token: string;
  userInfo: {
    id: number;
    username: string;
    // ... 其他用户信息
  };
  user_info: {};
  pay_account: {};
}

// gcash、maya登录 web登录
export const playerLogin = (data: LoginData) => {
  const {
    appPackageName,
    deviceId,
    deviceModel,
    deviceVersion,
    appVersion,
    sysTimezone,
    sysLanguage,
    source,
    isNative,
    telephoneCode,
  } = ALL_APP_SOURCE_CONFIG;
  const defaultData = {
    appPackageName,
    deviceId,
    deviceModel,
    deviceVersion,
    appVersion,
    sysTimezone,
    sysLanguage,
    source,
    isNative,
    telephoneCode,
    buds: "128", // web 128   maya/gcash 64
  };
  return http.post<LoginResult>(
    "/common/api/player/login",
    {
      ...defaultData,
      ...data,
    },
    {
      type: "formData",
      transformResult: (res) => res.data,
    }
  );
};

export const playerDetail = (data) => {
  return http.post("/common/api/player/detail", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};

// 发送验证码
// 1或2: 用户注册/登录
// 3: 修改用户登录密码
// 12: 绑定手机号
// 13 ：设置登录密码
// 14: 设置提现密码
// 15: 修改手机号
// 19：绑定提现账号
// 20: 修改提现账号
// ----这几种场景下需要先过Geetest
export const sendCodeMsg = (data = {}) => {
  return http.post("/common/api/sms/send/short/msg", data, {
    type: "formData",
  });
};

//  更新phone
export const changePhone = (data = {}) => {
  return http.post("/common/api/update/bind/phone", data, {
    type: "formData",
  });
};
//  绑定phone
export const bindPhone = (data = {}) => {
  return http.post("/common/api/player/add-bind", data, {
    type: "formData",
  });
};

// 获取用户信息状态
export const getUserStatus = (params) => {
  return http.get("/common/api/user/status", { params });
};

// 上传id图片
// font_side_url
export const imgUpload = (data = {}) => {
  return http.post("/common/api/img/upload", data, {
    type: "formData",
  });
};

// 获取kyc详情
export const getUserKyc = (data = {}) => {
  return http.post("/common/api/get/user/kyc", data, {
    type: "formData",
  });
};
// 更新用户信息
export const updateInfo = (data = {}) => {
  return http.post(
    "/common/api/sub/info",
    {
      is_no_goverment: 1,
      place_of_birth: "",
      branch: "",
      nationality: "",
      current_address: "",
      permanent_address: "",
      work: "",
      income: "",
      id_type: "",
      country: "",
      account_type: "",
      account_no: "",
      ...data,
    },
    {
      type: "formData",
    }
  );
};

//   更新支付密码
// withdraw_password:e10adc3949ba59abbe56e057f20f883e;
// buds:128;
export const withdrawPassword = (data = {}) => {
  return http.post("/common/api/set/withdraw/password", data, {
    type: "formData",
  });
};

//  校验手机号是否存在
export const phoneExists = (data = {}) => {
  return http.post("/common/api/get/phone/exists", data, {
    type: "formData",
  });
};
//  重置密码
export const setPWD = (data = {}) => {
  return http.post("/common/api/set/phone/passwd", data, {
    type: "formData",
  });
};

// 重置密码验证code
export const verifyCode = (data = {}) => {
  return http.post(
    "/common/api/sms/verify/short/msg/code",
    {
      telephoneCode: "+63",
      type: "3",
      ...data,
    },
    {
      type: "formData",
    }
  );
};

// 登出
export const logout = (data = {}) => {
  return http.post("/common/api/login/out", data, {
    type: "formData",
  });
};
// 刷新token
export const refreshToken = (data = {}) => {
  return http.post("/common/api/maya/refresh/token", data);
};
export const login = (data: LoginParams) => {
  return http.post<LoginResult>("/auth/login", data);
};

// 更新name、查询用户信息
export const setUserInfo = (data = {}) => {
  return http.post("/common/api/set/user/info", data, { transformResult: (res) => res.data });
};

// 验证是否购买成功
export const verifyPay = (data = {}) => {
  return http.post("/api/pay-service/verify-pay", data);
};

/**加载deposit列表数据 */
export const rechargeRecord = (params = {}) => {
  return http.get("/common/api/get/recharge/record", { params });
};

// 获取活动配置内容
export const getConfigEnum = (params = {}) => {
  return http.get("/avt/api/activity/adjustment/enum", { params });
};
/**加载 award 列表数据 */
export const awardRecord = (data = {}) => {
  return http.post("/common/api/get/award", data);
};
/**加载 withdraw 列表数据 */
export const exchangeRecord = (params = {}) => {
  return http.get("/common/api/exchange/list", { params });
};

// 获取组合订单列表内容
export const getCombineOrderList = (params = {}, id) => {
  return http.get(`/common/api/exchange/${id}`, {
    params,
    transformResult: (res) => res.data,
  });
};

/* *-----充值页面相关 end----- */
export const betOrder = (data = {}) => {
  return http.post("/vdr/api/bet-order", data, {
    type: "formData",
  });
};

//  首存红包弹窗数据
export const firstDepositBonusGuide = (data = {}) => {
  return http.post("/common/api/payment/first-deposit-bonus-guide", data, {
    type: "formData",
  });
};

// TODO:更新 获取下载引导配置
export const getDownloadGuideConfig = (data = {}) => {
  return http.post("/common/api/download/guide-config", data, {
    type: "formData",
  });
};

// Vip 详情页 进度
export const rebateProgress = (data = {}) => {
  return http.post("/avt/api/activity/rebate-progress", data, {
    type: "formData",
  });
};

// Vip 详情页 反水 列表  config_type: 4 vip反水  1 普通反水
export const rebateConf = (data = {}) => {
  return http.post(
    "/avt/api/activity/rebate-conf",
    { config_type: 4, ...data },
    {
      type: "formData",
    }
  );
};

// 头像列表
export const getAvatarList = (params = {}) => {
  return http.get("/open/api/user/avatar/list", { params });
};

// 更新头像
export const updateAvatar = (data = {}) => {
  return http.post("/common/api/player/update", data);
};
