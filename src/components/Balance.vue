<template>
  <div class="balance-wrap">
    <div ref="coinRef" class="coin" :class="{ pulsing: isAnimating }">
      <IconCoin :size="coinSize" />
    </div>
    <div ref="countUpContainer" class="balance" :style="{ fontSize: balanceSize + 'px' }">
      {{ displayValue }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue';
import { CountUp, CountUpOptions } from 'countup.js'; // 引入类型定义
import { useGlobalStore } from "@/stores/global";

// 全局状态
const globalStore = useGlobalStore();

/** 安全转换数值为数字 */
const safeFormatNumber = (val: any): number => {
  if (!val) return 0;
  const num = typeof val === 'string'
    ? parseFloat(val.trim().replace(/,/g, ''))
    : Number(val);
  return isNaN(num) ? 0 : num;
};

/** 格式化显示金额（千分位+2位小数） */
const formatDisplayValue = (num: number): string => {
  return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 父组件参数
const props = defineProps<{
  coinSize?: number;
  balanceSize?: number;
  animationDuration?: number;
}>();

// 内部状态
const coinRef = ref<HTMLElement | null>(null);
const countUpContainer = ref<HTMLElement | null>(null);
const countUpInstance = ref<CountUp | null>(null);
const isAnimating = ref(false);
const displayValue = ref('0.00');

// 默认配置
const defaultDuration = props.animationDuration ?? 1.5;

/** 创建countup配置 */
const createOptions = (start: number, end: number): CountUpOptions => ({
  startVal: start,
  endVal: end,
  duration: defaultDuration,
  useGrouping: true,
  separator: ',',
  decimal: '.',
  decimalPlaces: 2
});

// 初始化（无动画）
onMounted(() => {
  const container = countUpContainer.value;
  if (!container) return;

  const initialVal = safeFormatNumber(globalStore.balance);
  displayValue.value = formatDisplayValue(initialVal);

  // 实例化CountUp（2.9.0版本无需mount()）
  countUpInstance.value = new CountUp(
    container,
    initialVal, // 目标值（初始显示）
    createOptions(initialVal, initialVal)
  );

  // 关键：直接更新值，无需mount()
  countUpInstance.value.update(initialVal);
});

/** 启动动画（父组件调用） */
const startAnimation = async () => {
  const instance = countUpInstance.value;
  if (!instance) return;

  const currentStart = safeFormatNumber(globalStore.balance);
  const newBalance = await globalStore.getBalance()
  const currentEnd = safeFormatNumber(newBalance);

  isAnimating.value = true;

  if (currentStart !== currentEnd && currentEnd > 0) {
    displayValue.value = formatDisplayValue(currentEnd);
    await nextTick();
    // 更新值并触发动画（2.9.0版本直接调用update+start）
    instance.update(currentEnd, createOptions(currentStart, currentEnd));
    instance.start();
  }

  // 动画结束后重置
  setTimeout(() => { isAnimating.value = false; }, defaultDuration * 1000);
};

/** 停止动画 */
const stopAnimation = () => {
  isAnimating.value = false;
  countUpInstance.value?.pauseResume();
};

defineExpose({ startAnimation, stopAnimation, coinRef });
</script>

<style scoped>
/* 样式保持不变 */
.balance-wrap {
  display: flex;
  align-items: center;
  gap: 8px;
}

.coin {
  display: inline-block;

  &.pulsing {
    animation: pulse 0.2s infinite alternate;
  }
}

.balance {
  color: #222;
  font-family: D-DIN;
  font-weight: 700;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.2);
    filter: brightness(1.2);
  }
}
</style>
