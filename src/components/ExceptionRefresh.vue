<template>
  <div class="exception-refresh">
    <img src="@/assets/images/noWifi.png" alt="Network Error" />
    <div class="exception-title">{{ title }}</div>
    <div class="exception-message">{{ message }}</div>
    <ZButton class="refresh-btn" :disabled="isRefreshing || isInCooldown" :loading="isRefreshing"
      @click="handleRefresh">
      {{ refreshButtonText }}
    </ZButton>
    <div v-if="isInCooldown && !isRefreshing" class="cooldown-text">
      Please wait {{ remainingTime }}s before next refresh
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue';

interface Props {
  /** 标题文本 */
  title?: string;
  /** 错误消息 */
  message?: string;
  /** 刷新按钮文本 */
  refreshText?: string;
  /** 冷却时间（秒） */
  cooldownTime?: number;
  /** 是否显示冷却倒计时 */
  showCooldownText?: boolean;
}

interface Emits {
  /** 点击刷新按钮时触发 */
  (e: 'refresh'): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Oops !',
  message: 'There is an abnormality in the network',
  refreshText: 'Refresh',
  cooldownTime: 5,
  showCooldownText: true
});

const emit = defineEmits<Emits>();

// 响应式状态
const isRefreshing = ref(false);
const isInCooldown = ref(false);
const remainingTime = ref(0);
let cooldownTimer: NodeJS.Timeout | null = null;

// 计算属性
const refreshButtonText = computed(() => {
  if (isRefreshing.value) {
    return 'Refreshing...';
  }
  if (isInCooldown.value) {
    return `Wait ${remainingTime.value}s`;
  }
  return props.refreshText;
});

// 处理刷新点击
const handleRefresh = async () => {
  if (isRefreshing.value || isInCooldown.value) {
    return;
  }

  try {
    isRefreshing.value = true;

    // 触发刷新事件
    emit('refresh');

    // 模拟最小加载时间（可选）
    await new Promise(resolve => setTimeout(resolve, 500));

  } catch (error) {
    console.error('Refresh failed:', error);
  } finally {
    isRefreshing.value = false;
    startCooldown();
  }
};

// 开始冷却倒计时
const startCooldown = () => {
  if (props.cooldownTime <= 0) return;

  isInCooldown.value = true;
  remainingTime.value = props.cooldownTime;

  cooldownTimer = setInterval(() => {
    remainingTime.value--;

    if (remainingTime.value <= 0) {
      stopCooldown();
    }
  }, 1000);
};

// 停止冷却倒计时
const stopCooldown = () => {
  if (cooldownTimer) {
    clearInterval(cooldownTimer);
    cooldownTimer = null;
  }
  isInCooldown.value = false;
  remainingTime.value = 0;
};

// 重置状态（外部调用）
const reset = () => {
  isRefreshing.value = false;
  stopCooldown();
};

// 手动设置刷新状态（外部调用）
const setRefreshing = (refreshing: boolean) => {
  isRefreshing.value = refreshing;
  if (!refreshing) {
    startCooldown();
  }
};

// 组件卸载时清理定时器
onUnmounted(() => {
  stopCooldown();
});

// 暴露方法给父组件
defineExpose({
  reset,
  setRefreshing,
  isRefreshing: computed(() => isRefreshing.value),
  isInCooldown: computed(() => isInCooldown.value)
});
</script>
<style lang="scss" scoped>
.exception-refresh {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
  font-family: Inter;

  img {
    width: 112px;
    height: auto;
    margin-bottom: 16px;
  }

  .exception-title {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .exception-message {
    color: #999;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    margin-bottom: 24px;
    max-width: 280px;
  }

  .refresh-btn {
    min-width: 120px;
    height: 40px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 20px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cooldown-text {
    color: #666;
    font-size: 12px;
    margin-top: 12px;
    opacity: 0.8;
  }
}
</style>
