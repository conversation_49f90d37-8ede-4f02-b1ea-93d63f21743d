<template>
  <div class="wrap">
    <div class="left" @click="leftJump">
      <img :src="pagcor1" class="img1" alt="Pagcor标识1" loading="lazy">
      <img :src="pagcor2" class="img2" alt="Pagcor标识2" loading="lazy">
    </div>
    <!-- 新增竖线元素 -->
    <div class="vertical-line"></div>
    <div class="right">
      <img :src="pagcor4" class="img3" alt="Pagcor标识3" loading="lazy">
    </div>
  </div>
</template>

<script lang="ts" setup>
import pagcor1 from '@/assets/icons/login/pagcor1.png'
import pagcor2 from '@/assets/icons/login/pagcor2.png'
import pagcor4 from '@/assets/icons/login/pagcor4.png'

const leftJump = () => {
  window.open("https://www.pagcor.ph/regulatory/responsible-gaming.php", "_blank")
}
</script>

<style scoped lang="scss">
.wrap {
  display: flex;
  align-items: center;
  // justify-content: space-between;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
  gap: 16px;

  .left {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
  }

  .right {
    flex-shrink: 0;
  }

  // 竖线样式
  .vertical-line {
    width: 1px;
    height: 24px;
    background-color: #F0F0F0;
    margin: 0 8px;
  }

  .img1 {
    width: 32px;
    height: 32px;
    object-fit: contain;
  }

  .img2 {
    width: 90px;
    height: 21px;
    object-fit: contain;
  }

  .img3 {
    width: 97px;
    height: 29px;
    object-fit: contain;
  }
}
</style>
