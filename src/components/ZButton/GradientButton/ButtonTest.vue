<template>
  <div class="button-test">
    <h2>按钮渐变色测试</h2>
    
    <!-- 默认按钮 -->
    <div class="test-item">
      <h3>默认样式</h3>
      <GradientButton @click="handleClick">Copy Link</GradientButton>
    </div>
    
    <!-- 蓝色主题按钮 -->
    <div class="test-item">
      <h3>蓝色主题</h3>
      <GradientButton 
        text="蓝色按钮"
        background-gradient="linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)"
        border-gradient="linear-gradient(85deg, #e0f4ff 0%, #b3e5fc 50%, #81d4fa 100%)"
        @click="handleClick"
      />
    </div>
    
    <!-- 禁用状态 -->
    <div class="test-item">
      <h3>禁用状态</h3>
      <GradientButton 
        text="禁用按钮"
        background-gradient="linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)"
        border-gradient="linear-gradient(85deg, #e0f4ff 0%, #b3e5fc 50%, #81d4fa 100%)"
        disabled
        @click="handleClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import GradientButton from './btn.vue'

const handleClick = () => {
  console.log('按钮被点击了！')
}
</script>

<style lang="scss" scoped>
.button-test {
  padding: 40px 20px;
  max-width: 600px;
  margin: 0 auto;
  
  h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }
  
  .test-item {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    
    h3 {
      margin-bottom: 15px;
      color: #666;
      font-size: 16px;
    }
  }
}
</style>
