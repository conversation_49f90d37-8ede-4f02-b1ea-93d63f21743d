<template>
  <div class="gradient-demo">
    <h2>可配置渐变色按钮演示</h2>

    <!-- 默认样式 -->
    <div class="demo-section">
      <h3>默认样式</h3>
      <GradientButton @click="handleClick">默认按钮</GradientButton>
    </div>

    <!-- 蓝色主题 -->
    <div class="demo-section">
      <h3>蓝色主题</h3>
      <GradientButton
        text="蓝色按钮"
        background-gradient="linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)"
        border-gradient="linear-gradient(85deg, #e0f4ff 0%, #b3e5fc 50%, #81d4fa 100%)"
        @click="handleClick"
      />
    </div>

    <!-- 紫色主题 -->
    <div class="demo-section">
      <h3>紫色主题</h3>
      <GradientButton
        text="紫色按钮"
        background-gradient="linear-gradient(90deg, #a855f7 0%, #8b5cf6 100%)"
        border-gradient="linear-gradient(85deg, #f3e8ff 0%, #e9d5ff 50%, #ddd6fe 100%)"
        @click="handleClick"
      />
    </div>

    <!-- 绿色主题 -->
    <div class="demo-section">
      <h3>绿色主题</h3>
      <GradientButton
        text="绿色按钮"
        background-gradient="linear-gradient(90deg, #10b981 0%, #059669 100%)"
        border-gradient="linear-gradient(85deg, #d1fae5 0%, #a7f3d0 50%, #6ee7b7 100%)"
        @click="handleClick"
      />
    </div>

    <!-- 红色主题 -->
    <div class="demo-section">
      <h3>红色主题</h3>
      <GradientButton
        text="红色按钮"
        background-gradient="linear-gradient(90deg, #ef4444 0%, #dc2626 100%)"
        border-gradient="linear-gradient(85deg, #fee2e2 0%, #fecaca 50%, #fca5a5 100%)"
        @click="handleClick"
      />
    </div>

    <!-- 自定义禁用样式 -->
    <div class="demo-section">
      <h3>自定义禁用样式</h3>
      <GradientButton
        text="禁用按钮"
        background-gradient="linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)"
        border-gradient="linear-gradient(85deg, #e0f4ff 0%, #b3e5fc 50%, #81d4fa 100%)"
        disabled-gradient="linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 100%)"
        disabled
        @click="handleClick"
      />
    </div>

    <!-- 加载状态 -->
    <div class="demo-section">
      <h3>加载状态</h3>
      <GradientButton
        text="加载中..."
        background-gradient="linear-gradient(90deg, #a855f7 0%, #8b5cf6 100%)"
        border-gradient="linear-gradient(85deg, #f3e8ff 0%, #e9d5ff 50%, #ddd6fe 100%)"
        :loading="isLoading"
        @click="toggleLoading"
      />
    </div>

    <!-- 渐变方向演示 -->
    <div class="demo-section">
      <h3>不同渐变方向</h3>
      <div class="button-row">
        <GradientButton
          text="水平渐变"
          background-gradient="linear-gradient(90deg, #ff6b6b 0%, #feca57 100%)"
          border-gradient="linear-gradient(90deg, #ffe8e8 0%, #fff4e6 100%)"
          @click="handleClick"
        />
        <GradientButton
          text="垂直渐变"
          background-gradient="linear-gradient(180deg, #ff6b6b 0%, #feca57 100%)"
          border-gradient="linear-gradient(180deg, #ffe8e8 0%, #fff4e6 100%)"
          @click="handleClick"
        />
        <GradientButton
          text="对角渐变"
          background-gradient="linear-gradient(45deg, #ff6b6b 0%, #feca57 100%)"
          border-gradient="linear-gradient(45deg, #ffe8e8 0%, #fff4e6 100%)"
          @click="handleClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import GradientButton from "./index.vue";

const isLoading = ref(false);

const handleClick = () => {
  console.log("按钮被点击了！");
};

const toggleLoading = () => {
  isLoading.value = !isLoading.value;
  setTimeout(() => {
    isLoading.value = false;
  }, 3000);
};
</script>

<style lang="scss" scoped>
.gradient-demo {
  padding: 40px 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-size: 28px;
  }

  .demo-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 8px;
    background: transparent;

    h3 {
      margin-bottom: 15px;
      color: #666;
      font-size: 18px;
    }
  }

  .button-row {
    display: flex;
    gap: 16px;
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
