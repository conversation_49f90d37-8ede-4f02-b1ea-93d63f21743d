<template>
  <div
    :class="[
      'gradient-button-wrapper',
      {
        disabled: disabled,
        loading: loading,
      },
    ]"
    :style="wrapperStyle"
  >
    <button
      class="gradient-button"
      :disabled="disabled || loading"
      :style="buttonStyle"
      @click="handleClick"
    >
      <span v-if="loading" class="loading-spinner"></span>
      <span :class="{ 'loading-text': loading }">
        <slot>{{ text }}</slot>
      </span>
    </button>
  </div>
</template>

<script setup lang="ts" name="GradientButton">
import { computed } from "vue";

interface Props {
  text?: string;
  disabled?: boolean;
  loading?: boolean;
  // 背景渐变色配置
  backgroundGradient?: string;
  // 边框渐变色配置
  borderGradient?: string;
  // 禁用状态的背景渐变色
  disabledGradient?: string;
}

const props = withDefaults(defineProps<Props>(), {
  text: "Copy Link",
  disabled: false,
  loading: false,
  backgroundGradient: "linear-gradient(90deg, #ffbd55 0%, #ff572a 100%)",
  borderGradient: "linear-gradient(85deg, #fff0bf 0%, #ffe1c3 50%, #ffd3c8 100%)",
  disabledGradient: "linear-gradient(135deg, #d3d3d3 0%, #a9a9a9 100%)",
});

// 计算样式
const wrapperStyle = computed(() => ({
  background: props.disabled ? props.disabledGradient : props.borderGradient,
}));

const buttonStyle = computed(() => ({
  background: props.disabled ? props.disabledGradient : props.backgroundGradient,
}));

const emit = defineEmits<{
  click: [event: MouseEvent];
}>();

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit("click", event);
  }
};
</script>

<style lang="scss" scoped>
// 外层容器 - 负责边框效果
.gradient-button-wrapper {
  display: inline-block;
  padding: 3px;
  border-radius: 53px;
  // 背景色通过 :style 动态设置

  // 按下效果（保留触摸反馈）
  &:active:not(.disabled):not(.loading) {
    opacity: 0.8;
  }

  // 禁用状态
  &.disabled {
    opacity: 0.6;
    // 背景色通过 :style 动态设置
  }
}

// 内层按钮 - 负责内容和主要样式
.gradient-button {
  width: 100%;
  border: none;
  border-radius: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  // 渐变背景通过 :style 动态设置

  font-family: "Inter";
  font-weight: 700;
  font-size: 18px;
  // line-height: 48px;
  letter-spacing: 0px;
  text-align: center;

  min-width: 200px;
  padding: 10px;
  // min-height: 48px;

  // 文字样式
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  // 加载状态下的文字样式
  .loading-text {
    opacity: 0.7;
  }

  // 移除默认的焦点轮廓（移动端不需要）
  &:focus {
    outline: none;
  }
}

// 加载动画
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
