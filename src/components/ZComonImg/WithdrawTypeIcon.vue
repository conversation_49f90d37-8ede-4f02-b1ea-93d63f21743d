<!-- 提现/充值类型图标组件 -->
<template>
  <div class="withdraw-type-icon" :class="{ 'has-icon': hasValidIcon, 'fallback-icon': !hasValidIcon }"
    :style="containerStyle">
    <!-- 有效图标时显示图片 -->
    <van-image v-if="hasValidIcon" :src="finalIcon" :width="size" :height="size" fit="contain" class="icon-image"
      :alt="iconAltText">
      <template #loading>
        <div class="loading-placeholder">
          <ZIcon type="icon-qianbao1" color="#ccc" :size="size * 0.6" />
        </div>
      </template>
      <template #error>
        <div class="error-fallback">
          <img v-if="fallbackIcon" :src="fallbackIcon" :style="imageStyle" :alt="iconAltText" />
          <ZIcon v-else type="icon-qianbao1" color="#999" :size="size * 0.6" />
        </div>
      </template>
    </van-image>

    <!-- 无有效图标时显示默认图标 -->
    <div v-else class="default-icon">
      <ZIcon type="icon-qianbao1" color="#999" :size="size * 0.6" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import GCashLogo from "@/assets/images/gcash_icon.png";
import MayaLogo from "@/assets/images/maya_icon.png";
import { getServerSideImageUrl } from "@/utils/core/tools";

// 类型定义
interface Props {
  /** 背景颜色 */
  backgroundColor?: string;
  /** 图标宽度 */
  width?: number;
  /** 图标高度 */
  height?: number;
  /** 自定义图标URL */
  icon?: string;
  /** 类型标识 */
  type?: string;
}

const props = withDefaults(defineProps<Props>(), {
  backgroundColor: "#fff",
  width: 36,
  height: 36,
  icon: "",
  type: "",
});

// 内置图标映射
const BUILT_IN_LOGOS: Record<string, string> = {
  gcash: GCashLogo,
  maya: MayaLogo,
};

// 计算属性
const size = computed(() => Math.max(props.width, props.height));

const containerStyle = computed(() => ({
  background: props.backgroundColor,
  width: `${props.width}px`,
  height: `${props.height}px`,
}));

const imageStyle = computed(() => ({
  width: `${props.width}px`,
  height: `${props.height}px`,
}));

// 获取最终图标URL
const finalIcon = computed(() => {
  // 优先使用自定义图标
  if (props.icon) {
    return isAbsoluteUrl(props.icon) ? props.icon : getServerSideImageUrl(props.icon);
  }

  // 使用内置图标
  if (props.type && BUILT_IN_LOGOS[props.type.toLocaleLowerCase()]) {
    return BUILT_IN_LOGOS[props.type?.toLocaleLowerCase()];
  }

  return "";
});

// 获取备用图标
const fallbackIcon = computed(() => {
  return props.type && BUILT_IN_LOGOS[props.type?.toLocaleLowerCase()]
    ? BUILT_IN_LOGOS[props.type?.toLocaleLowerCase()]
    : "";
});

// 是否有有效图标
const hasValidIcon = computed(() => Boolean(finalIcon.value));

// 图标描述文本
const iconAltText = computed(() => {
  return props.type ? `${props.type} logo` : "Payment method logo";
});

// 工具函数
const isAbsoluteUrl = (url: string): boolean => {
  return /^https?:\/\//.test(url);
};
</script>

<style lang="scss" scoped>
.withdraw-type-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  transition: all 0.2s ease;

  &.has-icon {
    background: #fff;
  }

  &.fallback-icon {
    background-color: #f5f5f5;
  }

  // 悬停效果
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.icon-image {
  display: block;
  border-radius: 50%;
}

.loading-placeholder,
.error-fallback,
.default-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.loading-placeholder {
  background-color: #f8f9fa;
}

.error-fallback img {
  border-radius: 50%;
}
</style>
