<template>
  <van-dialog className="z-dialog" :show="visible" :title="title" :message="message" :close-on-click-overlay="false"
    :close-on-popstate="false" :before-close="beforeClose" :showConfirmButton="showConfirmButton"
    :showCancelButton="showCancelButton" :allowHtml="true" :width="330">
    <template #default>
      <!-- 自定义内容区域 -->
      <div :class="['custom-content', contentClass]">
        <!-- 插槽内容，优先级高于 message -->
        <slot v-if="$slots.default"></slot>
        <!-- 默认消息内容 -->
        <div v-if="message" class="message" v-html="message"></div>
        <div v-if="describe" class="describe" v-html="describe"></div>
      </div>
    </template>

    <!-- 自定义标题 -->
    <template #title v-if="title">
      <div class="custom-title" :class="{ flexCenter: !showClose }">
        <span class="title"> {{ title }}</span>
        <div class="close" @click="handleCancel" v-if="showClose">
          <ZIcon color="#999" type="icon-close" />
        </div>
      </div>
    </template>
    <!-- 自定义底部按钮 -->
    <template #footer v-if="showCancelButton || showConfirmButton">
      <div class="custom-footer">
        <!-- 取消按钮 -->
        <z-button type="default" :click="handleCancel" v-if="showCancelButton">
          {{ cancelText }}
        </z-button>
        <!-- 确认按钮 -->
        <z-button type="primary" :click="handleConfirm" v-if="showConfirmButton">
          {{ confirmText }}
        </z-button>
      </div>
    </template>
  </van-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import type { CSSProperties } from "@vue/runtime-dom";

/**
 * 组件属性定义
 */
interface Props {
  /** 弹窗标题 */
  title?: string;
  /** 弹窗消息内容（会被插槽覆盖） */
  message?: string;
  describe?: string;
  /** 控制弹窗显示/隐藏 */
  modelValue: boolean;
  /** 是否显示确认按钮 */
  showConfirmButton?: boolean;
  /** 是否显示取消按钮 */
  showCancelButton?: boolean;
  /** 是否显示取消按钮 */
  showClose?: boolean;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 确认按钮文本 */
  confirmText?: string;
  /** 确认回调函数（支持异步） */
  onConfirm?: () => Promise<void> | void;
  /** 取消回调函数（支持异步） */
  onCancel?: () => Promise<void> | void;
  // 顶部导航样式
  contentClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: "Tips",
  showConfirmButton: true,
  showCancelButton: true,
  showClose: false,
  cancelText: "Cancel",
  confirmText: "Confirm",
});

const emit = defineEmits<{
  /** 更新v-model绑定值 */
  (e: "update:modelValue", value: boolean): void;
}>();

/** 控制弹窗显示/隐藏 */
const visible = ref(props.modelValue);
/** 加载状态（用于防止异步操作未完成时关闭） */
const isLoading = ref(false);

/**
 * 监听modelValue变化
 */
watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
  }
);

/**
 * 关闭前的拦截处理
 * @param action 关闭动作类型
 * @returns 返回Promise<boolean>，true表示允许关闭，false表示阻止关闭
 */
const beforeClose = (action: string) => {
  return new Promise<boolean>((resolve) => {
    // 正在加载时阻止关闭
    resolve(!isLoading.value);
  });
};

/**
 * 确认按钮点击处理
 */
const handleConfirm = async () => {
  isLoading.value = true;
  try {
    // 执行父组件传入的onConfirm回调
    await props.onConfirm?.();
    // 关闭弹窗
    // visible.value = false;
    // emit("update:modelValue", false);
  } catch (error) {
    console.error("Dialog confirm error:", error);
  } finally {
    isLoading.value = false;
  }
};

/**
 * 取消按钮点击处理
 */
const handleCancel = async () => {
  isLoading.value = true;
  try {
    // 执行父组件传入的onCancel回调
    const canNotCloseed = await props.onCancel?.();
    if (canNotCloseed) return;
    // 关闭弹窗
    visible.value = false;
    emit("update:modelValue", false);
  } catch (error) {
    console.error("Dialog cancel error:", error);
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped lang="scss">
/* 底部按钮容器样式 */
.custom-footer {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 20px 20px;
}

/* 标题样式 */
.custom-title {
  display: flex;
  align-items: center;
  justify-content: space-between;

  &.flexCenter {
    justify-content: center;
  }

  .title {
    color: #222;
    text-align: center;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    padding-left: 20px;
    font-family: "PingFang SC";
  }

  .close {
    padding-right: 20px;
  }
}

:global(.van-dialog) {
  border-radius: 20px;
}

:global(.van-dialog__header) {
  padding-top: 20px;
}

/* 内容区域样式 */
.custom-content {
  padding: 20px;
  box-sizing: border-box;

  .message {
    color: #4f6477;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    // text-align: justify;
  }

  .describe {
    margin-top: 10px;
    color: #222;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}
</style>
