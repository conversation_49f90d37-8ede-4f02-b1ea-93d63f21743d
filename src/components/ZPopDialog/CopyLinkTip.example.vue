<template>
  <div class="example-page">
    <h2>CopyLinkTip 组件使用示例</h2>

    <!-- 基础使用 -->
    <div class="example-section">
      <h3>基础使用</h3>
      <button @click="showBasicExample" class="demo-btn">显示基础弹窗</button>
    </div>

    <!-- 自定义内容 -->
    <div class="example-section">
      <h3>自定义内容</h3>
      <button @click="showCustomExample" class="demo-btn">显示自定义弹窗</button>
    </div>

    <!-- 长链接示例 -->
    <div class="example-section">
      <h3>长链接示例</h3>
      <button @click="showLongUrlExample" class="demo-btn">显示长链接弹窗</button>
    </div>

    <!-- CopyLinkTip 组件 -->
    <CopyLinkTip
      v-model="showDialog"
      :copy-url="currentUrl"
      :title="currentTitle"
      :description="currentDescription"
      @close="handleClose"
      @copy-success="handleCopySuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
// @ts-ignore
import CopyLinkTip from "./CopyLinkTip.vue";
import { showToast } from "vant";

// 响应式数据
const showDialog = ref(false);
const currentUrl = ref("");
const currentTitle = ref("");
const currentDescription = ref("");

// 基础示例
const showBasicExample = () => {
  currentUrl.value = "https://example.com/download";
  currentTitle.value = "App Exclusive Rewards!";
  currentDescription.value = "Download now and unlock your special bonus.";
  showDialog.value = true;
};

// 自定义内容示例
const showCustomExample = () => {
  currentUrl.value = "https://myapp.com/special-offer";
  currentTitle.value = "Special Promotion!";
  currentDescription.value = "Get 50% off on your first purchase. Limited time offer!";
  showDialog.value = true;
};

// 长链接示例
const showLongUrlExample = () => {
  currentUrl.value =
    "https://very-long-domain-name.example.com/path/to/very/long/url/with/many/parameters?param1=value1&param2=value2&param3=value3";
  currentTitle.value = "Download Our App";
  currentDescription.value = "Access exclusive features and get the best experience.";
  showDialog.value = true;
};

// 事件处理
const handleClose = () => {
  console.log("弹窗已关闭");
};

const handleCopySuccess = (url: string) => {
  console.log("复制成功:", url);
  showToast("链接已复制到剪贴板！");
};
</script>

<style scoped lang="scss">
.example-page {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;

  h2 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
  }

  .example-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;

    h3 {
      color: #555;
      margin-bottom: 15px;
      font-size: 18px;
    }

    .demo-btn {
      background: linear-gradient(135deg, #ffb800 0%, #ff8a00 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 184, 0, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}
</style>
