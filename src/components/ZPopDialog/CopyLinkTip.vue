<template>
  <ZPopOverlay :show="showCopyLinkTip" @click="handleClose">
    <div class="copylink-wrap">
      <!-- 应用图标 -->
      <div class="app-icon-container">
        <img src="@/assets/images/popDialog/copylink.png" alt="" />
      </div>

      <div class="copy-link-container" @click.stop>
        <!-- 标题 -->
        <div class="title">App Exclusive Rewards!</div>

        <!-- 描述文字 -->
        <div class="description">Download now and unlock your special bonus.</div>

        <!-- URL显示区域 -->
        <div class="url-section">
          <div class="url-label">
            <ZIcon type="icon-lianjie" color="#666" :size="16" />
            <span> URL:</span>
          </div>
          <div class="url-content">
            {{ displayUrl }}
          </div>
        </div>

        <!-- 提示文字 -->
        <div class="copy-instruction">Copy the link and open it in your browser.</div>

        <!-- 复制按钮 -->
        <div class="copy-button" @click="handleCopyLink">Copy Link</div>

        <!-- 关闭按钮 -->
        <div class="close-btn" @click="handleClose">
          <ZIcon type="icon-guanbi2" color="#fff" :size="36" />
        </div>
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { useClipboard } from "@/hooks/useClipboard";

// Props定义
interface Props {
  /** 控制弹窗显示/隐藏 */
  modelValue?: boolean;
  /** 要复制的链接 */
  copyUrl?: string;
  /** 应用图标URL */
  appIcon?: string;
  /** 标题文字 */
  title?: string;
  /** 描述文字 */
  description?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  copyUrl: "https://example.com/download",
  title: "App Exclusive Rewards!",
  description: "Download now and unlock your special bonus.",
});

// Emits定义
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  close: [];
  "copy-success": [url: string];
}>();

// 响应式数据
const showCopyLinkTip = ref(props.modelValue);
const { copy } = useClipboard();

// 计算属性：显示的URL（截断长URL）
const displayUrl = computed(() => {
  const url = props.copyUrl;
  if (url.length > 30) {
    return url.substring(0, 30) + "...";
  }
  return url;
});

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    showCopyLinkTip.value = newVal;
  }
);

// 监听内部状态变化
watch(showCopyLinkTip, (newVal) => {
  emit("update:modelValue", newVal);
});

// 关闭弹窗
const handleClose = () => {
  showCopyLinkTip.value = false;
  emit("close");
};

// 复制链接
const handleCopyLink = async () => {
  try {
    await copy(props.copyUrl);
    emit("copy-success", props.copyUrl);
    // 复制成功后关闭弹窗
    handleClose();
  } catch (error) {
    console.error("Failed to copy link:", error);
  }
};

// 暴露方法供外部调用
defineExpose({
  show: () => {
    showCopyLinkTip.value = true;
  },
  hide: () => {
    showCopyLinkTip.value = false;
  },
});
</script>

<style lang="scss" scoped>
.copylink-wrap {
  position: relative;
}

.app-icon-container {
  position: absolute;
  top: 3%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  z-index: 9;
  img {
    width: 128px;
  }
}

.copy-link-container {
  font-family: "Inter";
  position: relative;
  width: 335px;
  background: #fff;
  border-radius: 20px;
  padding: 82px 24px 24px;
  box-sizing: border-box;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.title {
  color: #ff5100;
  margin-bottom: 12px;

  font-weight: 800;
  font-size: 22px;
  line-height: 100%;
  text-align: center;
}

.description {
  color: #666;
  margin-bottom: 18px;

  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
}

.url-section {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: #fff8f6;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 10px;
  text-align: left;

  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  > div {
    flex: 1;
  }

  .url-label {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    justify-content: flex-start;
    color: #666;

    span {
      font-weight: 500;
    }
  }

  .url-content {
    color: #ff5100;
    white-space: nowrap;
  }
}

.copy-instruction {
  color: #222;
  margin-bottom: 18px;

  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
}

.copy-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #ffb800 0%, #ff8a00 100%);
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  transition: all 0.2s ease;

  &:active {
    transform: translateY(0);
  }
}
.close-btn {
  position: absolute;
  bottom: -50px;
  right: 45%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}
</style>
