# 下载引导浮动提示组件 (DownloadGuideTip)

## 功能描述

这是一个底部固定的下载引导浮动提示组件，用于引导用户下载应用。组件独立于弹窗系统，完全由后台接口控制显示。

## 特性

- ✅ 底部固定显示，不影响页面内容
- ✅ 完全由后台接口控制显示/隐藏
- ✅ 独立于弹窗系统，层级低于弹窗组件
- ✅ 响应式设计，适配移动端
- ✅ 平滑的动画效果
- ✅ 集成 MobileWindowManager，确保下载链接在移动端正常打开
- ✅ 简化的配置，只需要基本的显示信息

## 使用方法

### 1. 在页面中引入组件

```vue
<template>
  <div>
    <!-- 其他页面内容 -->

    <!-- 下载引导提示 -->
    <DownloadGuideTip ref="downloadGuideRef" />
  </div>
</template>

<script setup>
import DownloadGuideTip from "@/components/ZPopDialog/DownloadGuideTip.vue";

// 组件引用
const downloadGuideRef = ref(null);

// 手动显示下载引导
const showDownloadGuide = () => {
  if (downloadGuideRef.value) {
    downloadGuideRef.value.show();
  }
};

// 手动隐藏下载引导
const hideDownloadGuide = () => {
  if (downloadGuideRef.value) {
    downloadGuideRef.value.hide();
  }
};
</script>
```

### 2. 后台配置数据结构

组件会通过 API 获取配置数据，数据结构如下：

```typescript
interface DownloadGuideConfig {
  is_enabled: boolean; // 是否启用下载引导（控制显示/隐藏）
  description: string; // 描述文字
  button_text: string; // 下载按钮文字
  download_url: string; // 下载链接
  app_icon: string; // 应用图标URL（可选）
}
```

**注意：**

- 应用名称固定为 "NUSTAR"，不需要后台配置
- 移除了每日显示次数限制，完全由 `is_enabled` 控制

### 3. API 接口

需要在后台实现以下 API 接口：

```
POST /common/api/download/guide-config
```

返回数据格式：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "is_enabled": true,
    "description": "Download the app, log in, and get ₱100 as a welcome bonus!",
    "button_text": "Download",
    "download_url": "https://play.google.com/store/apps/details?id=com.playmate.playzone",
    "app_icon": "https://example.com/app-icon.png"
  }
}
```

## 组件属性

组件支持以下方法：

- `show()`: 手动显示下载引导
- `hide()`: 手动隐藏下载引导

## 显示逻辑

1. 组件会在页面加载时自动获取后台配置
2. 如果配置中 `is_enabled` 为 `true`，则立即显示
3. 如果配置中 `is_enabled` 为 `false`，则不显示
4. 用户点击关闭或下载按钮后，组件隐藏

**简化说明：** 组件完全由后台接口的 `is_enabled` 字段控制，不涉及本地存储和次数限制。

## 样式特点

- 底部固定定位，距离底部 70px，避免遮挡导航栏
- 左右边距 12px，添加圆角 16px
- 渐变背景色（#AC1140 到 #D91A5B）
- 现代化 UI 设计，支持响应式布局
- 平滑的滑入动画效果
- **z-index 为 999，低于弹窗组件层级**

## 开发调试

在开发环境中，home 页面右上角会显示一个下载图标，点击可以手动触发下载引导显示，方便测试。

## 注意事项

1. 组件依赖 `MobileWindowManager` 来处理移动端下载链接打开
2. 需要确保后台 API 接口正常返回配置数据
3. 如果 API 失败，组件会使用默认配置并启用显示
4. 组件会自动处理移动端兼容性问题
