# ForceUpdateDialog 强制更新弹窗组件

基于 `CopyLinkTip` 组件开发的强制更新弹窗组件，用于提示用户进行应用更新。

## 功能特性

- 🚀 火箭图标设计，突出更新主题
- 📱 响应式设计，适配移动端
- 🎨 渐变背景，美观的视觉效果
- 📝 支持自定义更新内容列表
- 🔧 灵活的配置选项
- 🎯 支持强制更新和可选更新两种模式

## 组件 Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `modelValue` | `boolean` | `false` | 控制弹窗显示/隐藏 |
| `title` | `string` | `"Found a new version!"` | 弹窗标题 |
| `version` | `string` | `"V1.5.7"` | 版本号 |
| `descriptionTitle` | `string` | `"Update description:"` | 更新描述标题 |
| `updateList` | `string[]` | `["AAAAAA", "BBBBBB", "CCCCCC", "DDDDDD"]` | 更新内容列表 |
| `buttonText` | `string` | `"Update"` | 更新按钮文字 |
| `hasCloseBtn` | `boolean` | `false` | 是否显示关闭按钮 |
| `maskClosable` | `boolean` | `false` | 是否允许点击遮罩关闭 |

## 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `value: boolean` | 弹窗显示状态变化 |
| `close` | - | 弹窗关闭事件 |
| `update` | - | 点击更新按钮事件 |

## 组件方法

| 方法名 | 说明 |
|--------|------|
| `show()` | 显示弹窗 |
| `hide()` | 隐藏弹窗 |

## 使用示例

### 基础用法

```vue
<template>
  <ForceUpdateDialog
    v-model="visible"
    @update="handleUpdate"
    @close="handleClose"
  />
</template>

<script setup>
import { ref } from 'vue';
import ForceUpdateDialog from '@/components/ZPopDialog/ForceUpdateDialog.vue';

const visible = ref(false);

const handleUpdate = () => {
  // 处理更新逻辑
  console.log('开始更新');
};

const handleClose = () => {
  visible.value = false;
};
</script>
```

### 自定义配置

```vue
<template>
  <ForceUpdateDialog
    v-model="visible"
    title="发现新版本！"
    version="V2.0.0"
    description-title="更新内容："
    :update-list="updateList"
    button-text="立即更新"
    @update="handleUpdate"
  />
</template>

<script setup>
import { ref } from 'vue';

const visible = ref(false);
const updateList = [
  "修复了已知的安全漏洞",
  "优化了应用性能和稳定性", 
  "新增了用户反馈功能",
  "改进了界面设计和用户体验"
];

const handleUpdate = () => {
  // 跳转到应用商店或执行更新逻辑
  window.open('https://apps.apple.com/app/your-app');
};
</script>
```

### 可选更新模式

```vue
<template>
  <ForceUpdateDialog
    v-model="visible"
    :has-close-btn="true"
    :mask-closable="true"
    title="Optional Update Available"
    @update="handleUpdate"
    @close="handleClose"
  />
</template>
```

## 设计说明

- 组件采用与 `CopyLinkTip` 相同的设计语言和布局结构
- 使用火箭图标 🚀 突出更新主题
- 渐变背景色彩搭配，营造现代感
- 支持强制更新（无关闭按钮）和可选更新（有关闭按钮）两种模式
- 更新内容以列表形式展示，清晰易读

## 注意事项

1. 强制更新模式下，建议不设置 `hasCloseBtn` 和 `maskClosable` 为 `true`
2. 更新按钮点击后，组件不会自动关闭弹窗，需要外部控制
3. 建议在更新逻辑中处理实际的应用更新或跳转到应用商店
4. 组件依赖 `GradientButton` 和 `ZIcon` 组件，确保项目中已引入
