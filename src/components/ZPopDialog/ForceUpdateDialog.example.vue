<template>
  <div class="example-container">
    <h2>强制更新弹窗组件示例</h2>
    
    <div class="button-group">
      <button @click="showBasicDialog">显示基础更新弹窗</button>
      <button @click="showCustomDialog">显示自定义更新弹窗</button>
      <button @click="showClosableDialog">显示可关闭更新弹窗</button>
    </div>

    <!-- 基础更新弹窗 -->
    <ForceUpdateDialog
      v-model="basicDialogVisible"
      @update="handleUpdate"
      @close="handleClose"
    />

    <!-- 自定义更新弹窗 -->
    <ForceUpdateDialog
      v-model="customDialogVisible"
      :title="customConfig.title"
      :version="customConfig.version"
      :description-title="customConfig.descriptionTitle"
      :update-list="customConfig.updateList"
      :button-text="customConfig.buttonText"
      @update="handleCustomUpdate"
      @close="handleClose"
    />

    <!-- 可关闭更新弹窗 -->
    <ForceUpdateDialog
      v-model="closableDialogVisible"
      :has-close-btn="true"
      :mask-closable="true"
      title="Optional Update Available"
      version="V1.6.0"
      :update-list="['Performance improvements', 'Bug fixes', 'New features']"
      button-text="Update Now"
      @update="handleUpdate"
      @close="handleClose"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ForceUpdateDialog from './ForceUpdateDialog.vue';

// 弹窗显示状态
const basicDialogVisible = ref(false);
const customDialogVisible = ref(false);
const closableDialogVisible = ref(false);

// 自定义配置
const customConfig = ref({
  title: "发现新版本！",
  version: "V2.0.0",
  descriptionTitle: "更新内容：",
  updateList: [
    "修复了已知的安全漏洞",
    "优化了应用性能和稳定性", 
    "新增了用户反馈功能",
    "改进了界面设计和用户体验",
    "支持更多语言和地区"
  ],
  buttonText: "立即更新"
});

// 显示弹窗方法
const showBasicDialog = () => {
  basicDialogVisible.value = true;
};

const showCustomDialog = () => {
  customDialogVisible.value = true;
};

const showClosableDialog = () => {
  closableDialogVisible.value = true;
};

// 事件处理
const handleUpdate = () => {
  console.log('用户点击了更新按钮');
  // 这里可以执行实际的更新逻辑
  // 比如跳转到应用商店或下载更新包
  alert('开始更新...');
};

const handleCustomUpdate = () => {
  console.log('用户点击了自定义更新按钮');
  alert('开始自定义更新...');
};

const handleClose = () => {
  console.log('用户关闭了弹窗');
  basicDialogVisible.value = false;
  customDialogVisible.value = false;
  closableDialogVisible.value = false;
};
</script>

<style lang="scss" scoped>
.example-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h2 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.button-group {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

button {
  padding: 12px 24px;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}
</style>
