<template>
  <ZPopOverlay :show="showUpdateDialog" @click="handleClose">
    <div class="update-wrap">
      <!-- 火箭图标 -->
      <div class="app-icon-container">
        <div class="rocket-icon">🚀</div>
      </div>

      <div class="update-container top-bg" @click.stop>
        <div class="bottom-bg">
          <!-- 标题 -->
          <div class="title">{{ title }}</div>
          
          <!-- 版本号 -->
          <div class="version">{{ version }}</div>
          
          <!-- 更新描述 -->
          <div class="update-content">
            <div class="description-title">{{ descriptionTitle }}</div>
            <div class="update-list">
              <div 
                v-for="(item, index) in updateList" 
                :key="index" 
                class="update-item"
              >
                {{ index + 1 }}. {{ item }}
              </div>
            </div>
          </div>

          <!-- 更新按钮 -->
          <GradientButton :text="buttonText" @click="handleUpdate" />
        </div>
      </div>
      
      <!-- 关闭按钮 -->
      <div v-if="hasCloseBtn" class="close-btn" @click="handleClose">
        <ZIcon type="icon-guanbi2" color="#fff" :size="36" />
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
// @ts-ignore
import GradientButton from "@/components/ZButton/GradientButton/index.vue";

// Props定义
interface Props {
  /** 控制弹窗显示/隐藏 */
  modelValue?: boolean;
  /** 标题文字 */
  title?: string;
  /** 版本号 */
  version?: string;
  /** 更新描述标题 */
  descriptionTitle?: string;
  /** 更新内容列表 */
  updateList?: string[];
  /** 按钮文字 */
  buttonText?: string;
  /** 是否显示关闭按钮 */
  hasCloseBtn?: boolean;
  /** 是否允许点击遮罩关闭 */
  maskClosable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: "Found a new version!",
  version: "V1.5.7",
  descriptionTitle: "Update description:",
  updateList: () => ["AAAAAA", "BBBBBB", "CCCCCC", "DDDDDD"],
  buttonText: "Update",
  hasCloseBtn: false,
  maskClosable: false,
});

// Emits定义
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  close: [];
  update: [];
}>();

// 内部状态
const showUpdateDialog = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 监听内部状态变化
watch(showUpdateDialog, (newVal) => {
  emit("update:modelValue", newVal);
});

// 关闭弹窗
const handleClose = () => {
  if (props.maskClosable || props.hasCloseBtn) {
    showUpdateDialog.value = false;
    emit("close");
  }
};

// 更新按钮点击
const handleUpdate = () => {
  emit("update");
  // 通常强制更新后不关闭弹窗，由外部控制
};

// 暴露方法供外部调用
defineExpose({
  show: () => {
    showUpdateDialog.value = true;
  },
  hide: () => {
    showUpdateDialog.value = false;
  },
});
</script>

<style lang="scss" scoped>
.update-wrap {
  position: relative;
}

.app-icon-container {
  position: absolute;
  top: 3%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  z-index: 10;

  .rocket-icon {
    width: 128px;
    height: 128px;
    font-size: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 50%, #ffd23f 100%);
    border-radius: 50%;
    box-shadow: 0 8px 24px rgba(255, 107, 53, 0.3);
  }
}

.update-container,
.bottom-bg {
  font-family: "Inter";
  position: relative;
  width: 335px;
  min-height: 420px;
  background: #fff;
  border-radius: 20px;
  padding: 82px 24px 24px;
  box-sizing: border-box;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  z-index: 9;
  overflow: hidden;
  background: linear-gradient(90deg, #fffde9 0%, #fff3d3 50%, #ffe6e1 100%);
  
  .bottom-bg {
    position: absolute;
    width: 335px;
    min-height: 420px;
    z-index: 8;
    top: 0px;
    left: 0;
    background: linear-gradient(180deg, transparent 0%, #ffffff 55%);
  }
}

.title {
  color: #ff5100;
  margin-bottom: 8px;
  font-weight: 800;
  font-size: 22px;
  line-height: 100%;
  text-align: center;
}

.version {
  color: #ff5100;
  margin-bottom: 20px;
  font-weight: 600;
  font-size: 18px;
  line-height: 100%;
  text-align: center;
}

.update-content {
  margin-bottom: 24px;
  text-align: left;
}

.description-title {
  color: #222;
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
}

.update-list {
  .update-item {
    color: #666;
    margin-bottom: 8px;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    padding-left: 4px;
  }
}

.close-btn {
  position: absolute;
  bottom: -50px;
  right: 45%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}
</style>
