<template>
  <ZPopOverlay :show="showKycTip">
    <div class="content">
      <div class="title">KYC Verification</div>
      <div class="subtitle">Your account is not yet fully verified</div>
      <div class="desc">Your access to a certain service on the
        NUSTAR Online will be restricted.</div>
      <div class="btngroup">
        <ZButton type="default" @click="handleCancel">Cancel</ZButton>
        <ZButton @click="handleConfirm">Verify Now</ZButton>
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { watch } from "vue";
import { useAutoPopMgrStore, } from "@/stores/autoPopMgr";
import { useGlobalStore } from '@/stores/global'
import { KycMgr, InGameType } from '@/utils/KycMgr'
import router from "@/router";

const autoPopMgrStore = useAutoPopMgrStore();
const globalStore = useGlobalStore()
const { showKycTip, kycInfo } = storeToRefs(autoPopMgrStore);


const handleCancel = () => {
  showKycTip.value = false;
  globalStore.loginOut(false)
  autoPopMgrStore.hasPop = false;
  router.replace(`/`);
};

const handleConfirm = () => {
  // 理论是详版
  // if (kycInfo.value?.is_full == 0) {
  //   router.push(`/kyc/simple-form`);
  // } else if (kycInfo.value?.is_full == 1) {
  //   router.push(`/kyc/normal-form`);
  // }
  KycMgr.instance.inGameType = InGameType.Login;
  KycMgr.instance.checkBindPhone()
};

</script>

<style lang="scss" scoped>
.content {
  background: #fff;
  width: 330px;
  border-radius: 20px;
  padding: 20px;
  display: flex;
  flex-direction: column;

  .title {
    font-size: 18px;
    font-weight: 800px;
    text-align: center;
  }

  .subtitle {
    margin-top: 20px;
    font-size: 14px;
    text-align: center;

  }

  .desc {
    margin-top: 20px;
    font-size: 14px;
    color: #666;
    text-align: center;

  }

  .btngroup {
    margin-top: 20px;
    display: flex;
    gap: 10px;
  }
}
</style>
