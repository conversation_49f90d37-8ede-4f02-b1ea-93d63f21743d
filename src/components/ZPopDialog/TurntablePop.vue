<template>
  <!-- 大转盘 -->
  <ZPopOverlay :show="showSpinWheelTip">
    <div class="turntable-wrap">
      <div class="turntable-head">
        <!-- 顶部类型标题背景图 -->
        <img class="head-bg" src="@/assets/images/turnTable/turntable-head.png" alt="" />
        <!-- 顶部类型标题 -->
        <img class="type-text" :src="turnTableTargetData.headImg" alt="" />
      </div>
      <div class="turntable-content">
        <!-- 大转盘背景图 -->
        <img src="@/assets/images/turnTable/turntable-bg.png" alt="" />
        <!-- 转盘可转动部分 每格旋转 +72deg  -->
        <div class="rotate-img" :style="{ transform: `rotateZ(${targetResult.rotateDeg}deg)` }">
          <span
            :class="`rotate-num rotate-num${index + 1}`"
            v-for="(it, index) in turnTableTargetData.spin_activity_prize_config || []"
            :key="it.sort"
            v-show="index < 5"
            >₱{{ Number(it.prize).toLocaleString() }}</span
          >
        </div>
        <img class="rotate-active" src="@/assets/images/turnTable/checked.png" alt="" />
        <!-- 点击转动按钮Spin -->
        <img
          @click="getSpinResult"
          v-if="canSpinable"
          class="rotate-btn"
          src="@/assets/images/turnTable/center-btn.png"
          alt=""
        />
        <!-- disabled的按钮Spin -->
        <img
          v-else
          class="rotate-btn"
          @click="spinDisableClick"
          src="@/assets/images/turnTable/center-btn-disable.png"
          alt=""
        />
        <!-- 顶部指向箭头 -->
        <img class="rotate-arrow" src="@/assets/images/turnTable/arrow.png" alt="" />
        <div class="turntable-progress">
          <div
            class="progress"
            v-if="canSpinable"
            :style="{
              width: `${turnTableTargetData.progressInfo.percentage}%`,
            }"
          ></div>
          <div class="progress-text">
            {{
              canSpinable
                ? `${turnTableTargetData.progressInfo.hasBet}/${
                    turnTableTargetData.progressInfo.nextBet
                  } to start ${
                    turnTableTargetData.progressInfo.nextSpinName?.replace(" ", "") || ""
                  }`
                : "Unlock this Spin by making First Deposit."
            }}
          </div>
        </div>
        <div class="turntable-rollNum">
          <div>
            <div class="rollNum-title">Total Prize</div>
            ₱
            <!-- 滚动数字 -->
            <XNumberRoller :value="spinInfo.total_prize" textColor="#00f570" />
          </div>
          <div>
            <!-- 滚动字幕 中奖记录 -->
            <XTextRoller :getMore="getActivitySpinRecords" :rollerList="rollingRecords" />
          </div>
        </div>
      </div>
      <!-- 底部切换抽奖类型 根据betAmount判断是否可点击 -->
      <div class="turntable-btns">
        <div
          v-for="(it, idx) in btnsNum"
          :class="[
            `foot-btn btn${idx + 1}`,
            { active: turnTableTargetData.spin_activity_id === it.spin_activity_id },
          ]"
          @click="() => btnsClick(it)"
          :key="it.spin_activity_id"
        >
          ₱{{ Number(it.maxPrize).toLocaleString() }}
        </div>
        <!-- 底部切换抽奖类型 遮罩层 -->
        <div class="btns-overLay">
          <div
            v-for="(it, idx) in btnsNum"
            :class="`foot-btn btn${idx + 1}`"
            @click="() => btnsClick(it)"
            :key="it.spin_activity_name"
          ></div>
        </div>
      </div>
      <span class="turntable-close" @click="handleClose">
        <ZIcon type="icon-guanbi2" :size="30" />
      </span>
    </div>
  </ZPopOverlay>
</template>
<script lang="ts" setup>
// 底部左侧数字定时滚动计算在 src/components/Single-purpose/TurntableButton.vue 处理，数据公用，这边不做额外处理
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { useGlobalStore } from "@/stores/global";
import {
  getActivitySpinReceiveInfo,
  spinResult,
  getActivitySpinConfig,
  getActivitySpinConfigReceiveRecordPlatform,
} from "@/api/activity";
import { showToast } from "vant";
import XTextRoller from "@/components/Single-purpose/XTextRoller.vue";
import XNumberRoller from "@/components/Single-purpose/XNumberRoller.vue";
import { getRandomInt } from "@/utils/core/tools";
import { useAutoPopMgrStore, POP_FORM } from "@/stores/autoPopMgr";

interface SpinActivityPrizeConfig {
  spin_activity_prize_config_id: number | string;
  // 其他字段...
}
interface TurnTableObj {
  spin_activity_prize_config: SpinActivityPrizeConfig[];
}

const globalStore = useGlobalStore();
const autoPopMgrStore = useAutoPopMgrStore();
// 是否显示转盘弹窗  getSpinInfo 更新信息
const { showSpinWheelTip, spinInfo, sourceSpinWheel } = storeToRefs(autoPopMgrStore);

const handleClose = () => {
  showSpinWheelTip.value = false;
  if (sourceSpinWheel.value === POP_FORM.AUTO) {
    AutoPopMgr.destroyCurrentPopup();
  }
};

// 获取所有head-title图片
const imageModules = import.meta.glob("@/assets/images/turnTable/types/*.png", {
  eager: true,
});
// 图片转换为可用格式
const imageList = Object.entries(imageModules).map(([path, module]) => ({
  name: (path.split("/").pop() || "").split(".").shift() || "",
  url: (module as { default: string }).default,
}));

const turnTableAllCnts = ref({ bet_amount: 0 }); //转盘抽奖的所有内容
// 转盘的奖项内容
const turnTableTargetData = ref<TurnTableObj>({
  spin_activity_prize_config: [],
});
// 转盘的抽奖结果
const targetResult = ref<{
  targetIdx: number;
  targetSpinId?: number | string;
  rotateDeg: number | string;
}>({ targetIdx: 0, rotateDeg: 0 });

// 是否可以点击抽奖
const canSpinable = ref(false);
// 底下滚动记录字幕
const rollingRecords = ref([]);
// 底部抽奖类别展示按钮
const btnsNum = ref<any[]>([]);

// 获取每个 spin_activity_prize_config 数组内prize最大的对象
function getMaxPrizeConfigs(list: any[]) {
  return list.map((activity) => {
    const maxPrizeObj = activity.spin_activity_prize_config.reduce((prev: any, curr: any) => {
      return Number(curr.prize) > Number(prev.prize) ? curr : prev;
    });
    return {
      spin_activity_id: activity.spin_activity_id,
      spin_activity_name: activity.spin_activity_name,
      daily_betting_amount: activity.daily_betting_amount,
      maxPrize: maxPrizeObj.prize,
      maxPrizeObj,
    };
  });
}

// 根据 current_spin_activity_sort 获取 spin_activity 数组下的对应列表 spin_activity_prize_config
// 当 current_spin_activity_sort > spin_activity 数组长度，默认读取最后一个列表
const initData = async () => {
  getActivitySpinRecords();
  const res = await getActivitySpinReceiveInfo();
  if (!res.data) return; //报错处理
  const {
    spin_activity: spinList,
    current_spin_activity_sort: currentSort,
    is_first_recharge,
    bet_amount,
  } = res.data;
  turnTableAllCnts.value = res.data;
  const currentSpinData = spinList[currentSort < 5 ? currentSort : 4];
  const headImg =
    imageList.find((img) => img.name == currentSpinData.spin_activity_name.replace(" ", ""))?.url ||
    "";

  turnTableTargetData.value = {
    ...currentSpinData,
    headImg,
    progressInfo: {
      hasBet: bet_amount || "0",
      nextBet:
        bet_amount > currentSpinData.daily_betting_amount
          ? spinList[currentSort + 1].daily_betting_amount || "0"
          : currentSpinData.daily_betting_amount || "0",
      nextSpinName: spinList[currentSort + 1].spin_activity_name,
      percentage: bet_amount
        ? (Number(bet_amount) / Number(spinList[currentSort + 1].daily_betting_amount)) * 100
        : 0,
    },
  };
  // 获取最大prize对象数组
  const maxPrizeList = getMaxPrizeConfigs(spinList);
  btnsNum.value = maxPrizeList.sort((a, b) => Number(a.maxPrize) - Number(b.maxPrize)); // 升序
  canSpinable.value = is_first_recharge || bet_amount >= currentSpinData.daily_betting_amount;
  canSpinable.value = canSpinable.value ? spinInfo.value.left_times > 0 : canSpinable.value;
  if (spinList[4].daily_betting_amount <= turnTableAllCnts.value.bet_amount && canSpinable.value) {
    showToast("All the spins have been obtained, to use them!");
  }
};
// 获取抽奖记录
const getActivitySpinRecords = async () => {
  const res = await getActivitySpinConfigReceiveRecordPlatform();
  // TODO 多次请求后的数据处理优化
  rollingRecords.value = res.data?.list || [];
  spinInfo.value.total_prize = res.data?.total_prize || 0;
};

onBeforeMount(() => {
  initData();
});

// onUnmounted(() => {
//   clearInterval(timer);
//   timer = null;
// });

let totalRotate = 0;
const getSpinResult = async () => {
  const res = await spinResult({ spin_activity_id: turnTableTargetData.value.spin_activity_id });
  const targetSpinId = res.data.spin_activity_prize_id;
  const targetIdx = turnTableTargetData.value.spin_activity_prize_config.findIndex(
    (r) => r.spin_activity_prize_config_id === targetSpinId
  );
  const baseDeg = targetIdx * 72; // 5格，每格72°
  totalRotate += 360 * 3 + (baseDeg - (totalRotate % 360)); // 多转3圈再停到目标
  // 更新剩余次数
  await autoPopMgrStore.getSpinInfo();
  targetResult.value = {
    targetSpinId,
    targetIdx,
    rotateDeg: totalRotate,
  };
};
// 点击禁用spin按钮提示判断
const spinDisableClick = () => {
  if (!turnTableAllCnts.value.is_first_recharge) {
    showToast("Unlock this Spin by making First Deposit.");
    return;
  }
  if (turnTableAllCnts.value.bet_amount < turnTableTargetData.value.daily_betting_amount) {
    // TODO 提示语调整，按照差值提醒
    const diff = turnTableTargetData.value.daily_betting_amount - turnTableAllCnts.value.bet_amount;
    showToast(`Bet ₱ ${diff} to start  ${turnTableTargetData.value.spin_activity_name} !`);
  }
};
// 点击切换转盘抽奖选项弹出提示或者切换内容
const btnsClick = (row) => {
  if (row.daily_betting_amount > turnTableAllCnts.value.bet_amount) {
    showToast(`Bet ₱ ${row.daily_betting_amount} to start  ${row.spin_activity_name} !`);
    return;
  }
  const tar = turnTableAllCnts.value.spin_activity.find(
    (f) => f.spin_activity_id === row.spin_activity_id
  );
  turnTableTargetData.value = {
    ...tar,
    headImg:
      imageList.find((img) => img.name == tar.spin_activity_name.replace(" ", ""))?.url || "",
    progressInfo: {
      hasBet: turnTableAllCnts.value.bet_amount || "0",
      nextBet: tar.daily_betting_amount || "0",
      nextSpinName: tar.spin_activity_name,
      percentage: turnTableAllCnts.value.bet_amount
        ? (Number(turnTableAllCnts.value.bet_amount) / Number(tar.daily_betting_amount)) * 100
        : 0,
    },
  };
  let msg = "Please click the spin to start " + row.spin_activity_name;
  showToast(msg);
};
</script>
<style lang="scss" scoped>
.turntable-wrap {
  position: relative;
  transform: scale(0.9);

  .turntable-head {
    position: absolute;
    width: 100%;
    margin: 0 auto;
    z-index: 3;
    top: -25px;

    .head-bg {
      width: 90%;
      margin: 0 auto;
    }

    .type-text {
      position: absolute;
      top: 24vw;
      left: 32%;
      width: auto;
      height: 50px;
    }
  }

  .turntable-content {
    width: 100%;
    position: relative;

    img:first-child {
      width: 134%;
      max-width: 134% !important;
      margin-left: -15%;
      height: auto;
    }

    .rotate-arrow {
      position: absolute;
      width: 10%;
      top: 100px;
      left: 51%;
      z-index: 4;
      transform: translate(-40%, 0);
    }

    .rotate-img {
      position: absolute;
      top: 24%;
      width: 74%;
      height: 74vw;
      left: 15%;
      transition: transform 5s cubic-bezier(0.2, 0.8, 0.2, 1);
      background-image: url("../../assets/images/turnTable/turntable-content.png");
      background-repeat: no-repeat;
      background-size: cover;

      img {
        height: auto;
        width: 100%;
      }
    }

    .rotate-active {
      position: absolute;
      top: 117px;
      width: 49%;
      left: 27.5%;
    }
  }

  .turntable-progress {
    position: absolute;
    top: 116vw;
    left: 23%;
    width: 58%;
    height: 18px;
    margin: 0 auto;
    background: #250d00;
    border-radius: 100px;

    .progress {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      border-radius: 100px 0 0 100px;
      background: linear-gradient(to right, #d700e2, #0085ff);
    }

    .progress-text {
      line-height: 18px;
      color: #fff;
      font-size: 10px;
      position: absolute;
      z-index: 5;
      padding: 0 2px;
    }
  }

  .rotate-num {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff;
    font-size: 18px;
    font-weight: 700;
    display: inline-block;
    width: 32%;
    text-align: center;
  }

  .rotate-num1 {
    /*  顶部 */
    top: 17%;
    left: 50%;
    transform: translate(-50%, 0);
    -webkit-text-stroke: 1px #a94e00;
    text-stroke: 1px #a94e00;
    /* 兼容性阴影增强立体感 */
    text-shadow: 0 2px 8px #a94e00, 0 0 2px #fff, 1px 1px 0 #a94e00, -1px -1px 0 #a94e00;
  }

  .rotate-num2 {
    /* 右上 */
    top: 37%;
    right: 6%;
    transform: rotate(65deg);
    -webkit-text-stroke: 1px #0060c0;
    text-stroke: 1px #0060c0;
    /* 兼容性阴影增强立体感 */
    text-shadow: 0 2px 8px #0060c0, 0 0 2px #fff, 1px 1px 0 #0060c0, -1px -1px 0 #a94e00;
  }

  .rotate-num3 {
    /* 右下 */
    bottom: 23%;
    right: 19%;
    transform: rotate(140deg);
    -webkit-text-stroke: 1px #93009b;
    text-stroke: 1px #93009b;
    /* 兼容性阴影增强立体感 */
    text-shadow: 0 2px 8px #93009b, 0 0 2px #fff, 1px 1px 0 #93009b, -1px -1px 0 #a94e00;
  }

  .rotate-num4 {
    /* 左下 */
    bottom: 24%;
    left: 18%;
    transform: rotate(-145deg);
    -webkit-text-stroke: 1px #966c00;
    text-stroke: 1px #966c00;
    /* 兼容性阴影增强立体感 */
    text-shadow: 0 2px 8px #966c00, 0 0 2px #fff, 1px 1px 0 #966c00, -1px -1px 0 #a94e00;
  }

  .rotate-num5 {
    /* 左上 */
    top: 37%;
    left: 8%;
    transform: rotate(-75deg);
    -webkit-text-stroke: 1px #569c00;
    text-stroke: 1px #569c00;
    /* 兼容性阴影增强立体感 */
    text-shadow: 0 2px 8px #569c00, 0 0 2px #fff, 1px 1px 0 #569c00, -1px -1px 0 #a94e00;
  }

  .turntable-rollNum {
    width: 74%;
    margin: 0 auto;
    position: absolute;
    bottom: 4px;
    left: 15%;
    font-size: 18px;
    color: #00f570;
    display: flex;
    justify-content: space-around;
    --van-rolling-text-color: #00f570;
    --van-rolling-text-font-size: 18px;
    --van-rolling-text-item-width: 10px;
    gap: 10px;

    .rollNum-title {
      font-size: 10px;
      color: #333;
      font-weight: 800;
    }

    .rolling-number {
      font-size: 14px;
      padding: 0;
    }

    > div {
      width: 50%;
      // padding-top: 16px;
      text-align: center;

      // margin-right: 8px;
      &:last-child {
        height: 13vw;
        padding-top: 0;
        margin-right: 0;
      }
    }
  }

  .rotate-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 110px;
    height: auto;
    transform: translate(-50%, -50%);
  }

  .turntable-btns {
    display: flex;
    position: relative;
    justify-content: center;
    gap: 0;
    margin-top: 6px;
    margin-left: 26px;
    height: 54px;

    .btns-overLay {
      display: block;
      position: absolute;
      top: 0;
      left: 5px;
      width: 93%;
      height: 100%;
      background: rgba(0, 4, 8, 0.5);

      .foot-btn {
        background: none !important;
      }
    }

    .foot-btn {
      position: absolute;
      height: 54px;
      display: flex;
      font-size: 12px;
      align-items: flex-end;
      color: #fff;
      text-align: center;
      justify-content: center;
      background-size: contain;
      background-repeat: no-repeat;
      padding-bottom: 3px;

      &.btn1 {
        left: 6px;
        width: 56px;
        background-image: url("../../assets/images/turnTable/btn-1.svg");
        justify-content: start;
        padding-left: 8px;
      }

      &.active {
        z-index: 5;
      }

      &.btn2 {
        background-image: url("../../assets/images/turnTable/btn-2.svg");
        width: 67px;
        left: 14.8%;
      }

      &.btn3 {
        background-image: url("../../assets/images/turnTable/btn-3.svg");
        width: 71px;
        left: 31.3%;
      }

      &.btn4 {
        background-image: url("../../assets/images/turnTable/btn-4.svg");
        width: 79px;
        left: 49.3%;
      }

      &.btn5 {
        background-image: url("../../assets/images/turnTable/btn-5.svg");
        width: 86px;
        left: 69.6%;
      }
    }
  }

  .turntable-close {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
