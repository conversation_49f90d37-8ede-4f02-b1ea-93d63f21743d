<template>
  <div class="provider-selector">
    <div>
      <slot :clickFunc="openPopup"></slot>
    </div>
    <ZActionSheet
      :closeOnClickOverlay="false"
      v-model="dialogVisible"
      title="Select a Game Provider"
      :onConfirm="handleConfirm"
      :onCancel="handleCancel"
      :onClose="handleClose"
      cancelText="Clear Filters"
      :closeOnClickCancelBtn="false"
    >
      <div class="content">
        <van-checkbox-group direction="horizontal" v-model="checkedOptions" class="checkbox-group">
          <van-checkbox name="all" key="all" @toggle="() => clickCheck('all')" class="checkbox">
            <template #icon>
              <div class="checkitem-All"><ZIcon type="icon-type" color="#666666"></ZIcon> All</div>
            </template>
          </van-checkbox>
          <van-checkbox
            v-for="category in chooseProviders"
            :key="`${category.id}`"
            :name="category.id"
            @toggle="() => clickCheck(category.id)"
            class="checkbox"
          >
            <template #icon>
              <div class="provider-checkitem">
                <template v-if="category.imageUrl && !category.imageError">
                  <van-image
                    :src="category.imageUrl"
                    @load="category.imageError = false"
                    @error="category.imageError = true"
                    :loading="category.short_name || category.provider"
                  />
                </template>
                <div class="checkitem-All" v-if="category.imageError">
                  {{ category.short_name || category.provider }}
                </div>
              </div>
            </template>
          </van-checkbox>
        </van-checkbox-group>
      </div>
    </ZActionSheet>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useGameStore } from "@/stores/game";

const gameStore = useGameStore();
const { chooseProviders } = storeToRefs(gameStore);

const props = defineProps<{
  checkedProviders: string[];
  visible: boolean;
  confirm: Function;
}>();

// 定义 emits 事件
const emit = defineEmits(["update:visible"]);
// 选中项
const checkedOptions = ref<string[] | number[]>(
  props.checkedProviders.length > 0 ? props.checkedProviders : ["all"]
);
watch(
  () => props.checkedProviders,
  (newVal) => {
    checkedOptions.value = newVal.includes("all") ? newVal : newVal.map(Number);
  }
);

// 弹窗显示、隐藏，直接用 props.visible 控制，不再自己维护 dialogVisible
const dialogVisible = computed({
  get: () => props.visible,
  set: (val: boolean) => emit("update:visible", val),
});

// 打开弹窗、初始化赋值
const openPopup = (value = true) => {
  emit("update:visible", true);
};
// 筛选数值赋值、关闭弹窗
const handleConfirm = () => {
  emit("update:visible", false);
  const checkeds = checkedOptions.value.length > 0 ? checkedOptions.value : ["all"];
  props.confirm(checkeds);
};
const handleCancel = () => {
  // 清空选中的类别
  checkedOptions.value = ["all"];
};
// 筛选项变化
const clickCheck = (val) => {
  if (val !== "all" && checkedOptions.value.includes(val)) {
    checkedOptions.value = [...checkedOptions.value.filter((v) => v !== "all")];
  } else if (val == "all") {
    checkedOptions.value = ["all"];
  }
};

const handleClose = () => {
  checkedOptions.value = props.checkedProviders;
};
</script>
<style lang="scss" scoped>
.content {
  .checkbox {
    margin-bottom: 6px;
  }
  &:deep(.van-checkbox-group) {
    width: 100%;
    display: flex;
    .van-checkbox--horizontal {
      margin: 0;
      margin-bottom: 6px;
    }

    .van-checkbox {
      flex: 1 1 25%;
    }

    .van-checkbox__icon {
      padding: 3px;
      height: 52px;
      text-align: center;
      line-height: 40px;
      border: 3px solid transparent;
      font-size: 16px;
      border-radius: 18px;

      &.van-checkbox__icon--checked {
        border-color: #ac1140;
      }
    }

    .checkitem-All {
      width: 70px;
      color: #666666;
      background-color: #f9eef2;
      border-radius: 12px;
    }

    .provider-checkitem {
      border-radius: 12px;
      color: #666666;
      width: 70px;
      height: 40px;
      overflow: hidden;
    }

    .van-image {
      width: 100%;
    }

    .van-image__img {
      width: 100%;
      height: 40px;
    }

    .van-image__loading {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
