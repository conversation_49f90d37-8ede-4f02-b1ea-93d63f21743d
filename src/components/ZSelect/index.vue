<template>
  <div>
    <!-- 选择器显示部分 -->
    <div class="select" @click="handleSelect">
      <div v-if="getSelectedLabel" class="value">
        {{ getSelectedLabel }}
      </div>
      <div class="placeholder" v-else>{{ placeholder }}</div>
      <ZIcon type="icon-xiangxia" color="#999" class="select-icon"></ZIcon>
    </div>
    <!-- 底部弹出选择框 -->
    <ZActionSheet class="z-action-select" v-model="dialogVisible" :showCancelButton="false" :title="title"
      @confirm="handleConfirm" @cancel="handleCancel" @close="handleCancel" :closeOnClickOverlay="true"
      :style="{ '--van-action-sheet-max-height': '80vh' }">
      <ZRadioGroup v-model="currentValue" name="nationality">
        <ZRadio v-for="(item, index) in normalizedSelectList" :key="index" :label="item.label" :value="item.value" />
      </ZRadioGroup>
    </ZActionSheet>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, ref, watch } from 'vue';

// 定义 fieldNames 接口
interface FieldNames {
  label?: string;
  value?: string;
}

// 定义 props
const props = defineProps({
  title: {
    type: String,
    default: '',
    required: true,
  },
  selectList: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: [String, Number] as PropType<string | number | null>,
    default: null,
  },
  placeholder: {
    type: String,
    default: 'Please select',
  },
  fieldNames: {
    type: Object as PropType<FieldNames>,
    default: () => ({
      label: 'label',
      value: 'value',
    }),
  },
});

// 定义 emits
const emits = defineEmits(['update:modelValue', 'confirm']);

// 内部状态：当前选中的值（用于弹窗内的临时选择）
const currentValue = ref(props.modelValue);

// 内部状态：显示的值（用于显示，只有确认后才更新）
const displayValue = ref(props.modelValue);

// 控制弹窗显示
const dialogVisible = ref(false);

// 安全获取对象嵌套属性的辅助函数
const getNestedValue = (obj: any, path: string) => {
  if (!obj || !path) return obj;
  return path.split('.').reduce((acc, key) => acc && acc[key], obj);
};

// 规范化后的选项列表
const normalizedSelectList = computed(() => {
  if (!props.selectList || props.selectList.length === 0) return [];

  // 判断数组第一项的类型，来决定如何处理
  const firstItem = props.selectList[0];

  // 情况1：数组元素是字符串或数字，例如 ['option1', 'option2']
  if (typeof firstItem === 'string' || typeof firstItem === 'number') {
    return props.selectList.map(item => ({
      label: String(item),
      value: item
    }));
  }

  // 情况2：数组元素是对象，使用 fieldNames 配置转换
  if (typeof firstItem === 'object' && firstItem !== null) {
    const { label: labelPath = 'label', value: valuePath = 'value' } = props.fieldNames;

    return props.selectList.map(item => ({
      label: getNestedValue(item, labelPath) !== undefined
        ? String(getNestedValue(item, labelPath))
        : String(item),
      value: getNestedValue(item, valuePath) !== undefined
        ? getNestedValue(item, valuePath)
        : item,
    }));
  }

  // 默认返回空数组
  return [];
});

// 计算属性：获取选中项的 label（基于显示值）
const getSelectedLabel = computed(() => {
  const selectedItem = normalizedSelectList.value.find(item => item.value === displayValue.value);
  return selectedItem?.label || '';
});

// 监听父组件传入值的变化
watch(() => props.modelValue, (newVal) => {
  currentValue.value = newVal;
  displayValue.value = newVal;
});

// 打开选择器
const handleSelect = () => {
  dialogVisible.value = true;

  // 设置弹窗内的临时选择值
  if (displayValue.value) {
    // 如果有显示值，使用显示值作为临时选择
    currentValue.value = displayValue.value;
  } else if (normalizedSelectList.value.length > 0) {
    // 如果没有显示值，默认选中第一项（仅在弹窗内显示，不影响实际值）
    currentValue.value = normalizedSelectList.value[0].value;
  }
};

// 确认选择
const handleConfirm = () => {
  dialogVisible.value = false;

  // 获取选中项
  const selectedItem = normalizedSelectList.value.find(item => item.value === currentValue.value);

  // 只有点击确认时才更新显示值和实际值
  displayValue.value = currentValue.value;

  // 触发事件，传递选中的 label 和 value
  emits('confirm', {
    label: selectedItem?.label || '',
    value: currentValue.value
  });

  // 更新 v-model 值
  emits('update:modelValue', currentValue.value);
};

// 处理弹窗关闭（点击遮罩或取消按钮）
const handleCancel = () => {
  dialogVisible.value = false;
  // 恢复到显示值，不保存临时选择
  currentValue.value = displayValue.value;
};
</script>

<style lang="scss" scoped>
.select {
  width: 100%;
  height: 42px;
  box-sizing: border-box;
  background-color: #f7f8fa;
  border-radius: 999px;
  display: inline-flex;
  padding: 12px 20px;
  align-items: center;
  flex-shrink: 0;
  color: #222;
  font-family: D-DIN;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: -0.3px;
  position: relative;

  .placeholder {
    color: #C0C0C0;
    /* 输入框内默认字体 */
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }



  .select-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>

<style lang="scss">
// 全局样式，用于覆盖 Vant ActionSheet 的高度限制
// .z-action-select {

//   // 直接设置最大高度
//   .van-action-sheet__content {
//     max-height: 70vh !important;
//     overflow-y: auto;
//   }

//   // 如果有标题，需要减去标题高度
//   .van-action-sheet__header+.van-action-sheet__content {
//     max-height: calc(70vh - 60px) !important;
//   }

//   // 确保滚动区域正确
//   .van-radio-group {
//     max-height: inherit;
//     overflow-y: auto;
//   }
// }
</style>
