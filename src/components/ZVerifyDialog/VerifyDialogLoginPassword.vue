<!-- 登陆密码设置弹窗 -->
<template>
  <ZActionSheet v-model="visible" title="Set Login Password" :showCancelButton="false" :onConfirm="handleConfirm"
    :onCancel="handleCancel">
    <div class="dialog-content">
      <div class="phone-input">
        <div class="set-password-tip">
          <div v-show="dialogStep === 1">Set your password</div>
          <div v-show="dialogStep === 2">Re-enter the same password</div>
        </div>
        <ZPasswordInput2 v-if="dialogStep === 1" v-model="loginPassword" />
        <ZPasswordInput2 v-else v-model="loginPasswordRepeat" />
        <div v-show="errorMessage" class="error-tip">
          {{ errorMessage }}
        </div>
      </div>
    </div>
  </ZActionSheet>
</template>

<script setup lang="ts">
import { GeetestMgr, GEETEST_TYPE } from "@/utils/GeetestMgr";
import { useGlobalStore } from "@/stores/global";
import { ref, onUnmounted } from "vue";
import { hasNumberAndCharacter } from "@/utils/core/tools";
import { Md5 } from "@/utils/core/Md5";
import { setPWD } from "@/api/user";
import { showToast } from "vant";

const props = defineProps({
  // 显示弹窗
  showNextDialog: {
    type: Boolean,
    default: false,
    required: false,
  },
  // 成功回调
  succCallBack: {
    type: Function,
    default: () => { },
    required: false,
  },
  // 成功提示
  toastText: {
    type: String,
    default: "Payment password set successfully",
    required: false,
  },
  // 忘记密码
  phone: {
    type: String,
    default: "",
    required: false,
  },
});

// 全局状态
const globalStore = useGlobalStore();
const userInfo = ref(globalStore.userInfo);
// 响应式数据
const dialogStep = ref(1); // 步骤：1-验证码 / 2-设密码 / 3-确认密码
const loginPassword = ref(""); // 登陆密码（第一次）
const loginPasswordRepeat = ref(""); // 登陆密码（第二次）
const errorMessage = ref("");
// 弹窗是否显示
const visible = ref(props.showNextDialog);
let countdownTimer: NodeJS.Timeout | null = null; // 倒计时定时器

watch(
  () => props.showNextDialog,
  (val) => {
    visible.value = val;
    if (!val) {
      resetData();
    }
  }
);

const emit = defineEmits(["update:showNextDialog", "complete"]);

const handleCancel = () => {
  emit("update:showNextDialog", false);
};

const resetData = () => {
  dialogStep.value = 1;
  loginPassword.value = "";
  loginPasswordRepeat.value = "";
  errorMessage.value = "";
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
};

/**
 * 通用极验校验函数
 * @param loginType 极验类型
 * @param callback  校验成功后的回调
 */
const geetestValidate = (loginType: string, callback: Function) => {
  if (!loginType) return;

  GeetestMgr.instance.geetest_device(loginType, (successRes) => {
    if (successRes) {
      callback({
        geetest_guard: successRes.geetest_guard || "",
        userInfo: successRes.userInfo || "",
        geetest_captcha: successRes.geetest_captcha || "",
        buds: successRes.buds || "64",
      });
    }
  });
};

const vaildErrMsg = (inputValue) => {
  let err = "";
  if (!inputValue) {
    err = "Password can not be empty";
  } else if (
    inputValue.length < 8 ||
    inputValue.length > 20 ||
    !hasNumberAndCharacter(inputValue)
  ) {
    err = "The login password is 8~20 characters and must contain letters and numbers.";
  }
  return err;
};

const handleConfirm = () => {
  if (dialogStep.value === 1) {
    let errMsg = vaildErrMsg(loginPassword.value);
    errorMessage.value = errMsg;
    if (errMsg) return;
    dialogStep.value = 2;
  } else {
    let errMsg = vaildErrMsg(loginPasswordRepeat.value);
    errorMessage.value = errMsg;
    if (errMsg) return;
    if (loginPassword.value !== loginPasswordRepeat.value) {
      errMsg = "The passwords entered twice are inconsistent";
      errorMessage.value = errMsg;
      return;
    }
    let geeid = GEETEST_TYPE.first_password;
    if (userInfo.value.login_password) {
      geeid = GEETEST_TYPE.forget_password;
    }
    if (!globalStore.token) {
      geeid = GEETEST_TYPE.forget_password;
    }
    geetestValidate(geeid, resetPassword);
  }
};

const resetPassword = async (ret) => {
  let gee_guard = ret?.geetest_guard || "";
  let uInfo = ret?.userInfo || "";
  let gee_captcha = ret?.geetest_captcha || "";
  let isForgetPw = userInfo?.value?.login_password;
  if (!globalStore.token) {
    isForgetPw = true; //这里有个bug 如果没有设置登陆密码的时候
    // 没有登陆的时候 点击forget
  }
  let params = {
    phone: props.phone || userInfo?.value.phone,
    telephone_code: "+63",
    // "verify_code": this.m_code,//同时要验证验证码 bug
    upd_column: isForgetPw ? "forget_password" : "password",
    password: Md5.hashStr(loginPassword.value).toString(),
    geetest_guard: gee_guard,
    userInfo: uInfo,
    geetest_captcha: gee_captcha,
    buds: ret?.buds || "64",
  };
  await setPWD(params);
  showToast("Login password set successfully");
  globalStore.updateUserInfo({
    login_password: 1,
  });
  emit("complete");
  props.succCallBack && props.succCallBack(loginPassword.value);
};
</script>

<style scoped lang="scss">
.dialog-content {
  padding-bottom: 20px;

  .set-password-tip {
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 16px;
    /* 171.429% */
  }

  // 密码步骤样式
  .phone-input {
    .error-tip {
      color: #e5110a;
      font-size: 12px;
      margin-top: 4px;
    }
  }
}
</style>
