<!-- 添加、编辑账户弹窗 -->
<template>
  <ZActionSheet v-model="visible" :title="getTypeInfo?.title" confirmText="Confirm" :showCancelButton="false"
    :onConfirm="handleConfirm" :onCancel="handleCancel">

    <div class="dialog-content">
      <div class="send-code-tip">
        A text message with a 6-digit code was just sent to
        <b>{{ formattedUserPhone }}</b>
      </div>
      <div class="phone-input-code">
        <label for="verCode">Enter a verification code</label>
        <div class="phone-input-container">
          <input id="verCode" v-model="verificationCode" maxlength="6" type="tel" placeholder="Enter the code"
            @input="handleCodeInput" />
          <ZButton class="get-code-btn" :disabled="isCounting" @click="checkPhoneIsRegister">
            {{ isCounting ? `${count}s` : "Get Code" }}
          </ZButton>
        </div>
      </div>
    </div>
  </ZActionSheet>
</template>

<script setup lang="ts">
import {
  sendCodeMsg,
  verifyCode,
} from '@/api/setPhoneNumber'
import { GeetestMgr, GEETEST_TYPE } from "@/utils/GeetestMgr";
import { useGlobalStore } from "@/stores/global";
import { ref, computed } from "vue";
import { showToast } from "vant";
import { GlobalEnum } from '@/utils/config/GlobalEnum'
import { PN_VERIFY_TYPE } from './types'


const globalStore = useGlobalStore()
const { userInfo } = storeToRefs(globalStore)

const props = defineProps({
  // 显示弹窗
  showDialog: {
    type: Boolean,
    default: false,
    required: true,
  },
  //  类型:添加：3 、编辑 4
  verifyType: {
    type: Number,
    default: 3,
    required: true,
  },
  // 成功回调
  succCallBack: {
    type: Function,
    default: () => { },
    required: false,
  },
})

// 弹窗是否显示
const visible = ref(props.showDialog);
// 验证码
const verificationCode = ref('');
// 是否已发送验证码
const hasSentCode = ref(false);
// 是否正在倒计时
const isCounting = ref(false);
// 倒计时数值
const count = ref(60);
// 倒计时定时器
let countdownTimer: NodeJS.Timeout | null = null;

watch(
  () => props.showDialog,
  (val) => {
    visible.value = val;
    if (!val) {
      resetData()
    }
  }
);

const getTypeInfo = computed(() => {
  if (props.verifyType === PN_VERIFY_TYPE.AddWithdrawAccount) {
    return {
      title: 'Add Fund Account',
      gtype: GEETEST_TYPE.bind_withdraw_account_code,
      msgType: GlobalEnum.SMS_TYPE.BIND_WITHDRAW_ACCOUNT,
    }
  } else if (props.verifyType === PN_VERIFY_TYPE.ChangeWithdrawAccount) {
    return {
      title: 'Change Fund Account',
      gtype: GEETEST_TYPE.change_withdraw_account_code,
      msgType: GlobalEnum.SMS_TYPE.UPDATE_WITHDRAW_ACCOUNT,
    }
  }
})

// 处理验证码输入，只允许数字且限制6位
const handleCodeInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  let value = target.value;

  // 只保留数字
  value = value.replace(/\D/g, '');

  // 限制最多6位
  if (value.length > 6) {
    value = value.slice(0, 6);
  }

  verificationCode.value = value;
  target.value = value;
};

const resetData = () => {
  hasSentCode.value = false
  isCounting.value = false
  count.value = 60
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
}

const formattedUserPhone = computed(() => {
  const newPhone = userInfo.value.phone || '';
  return newPhone.slice(0, 2) + "****" + newPhone.slice(6, 10);
});


//发送验证码之前先 geetest
const checkPhoneIsRegister = () => {
  GeetestMgr.instance.geetest_device(GEETEST_TYPE.bind_withdraw_account_code, (succ) => {
    if (succ) {
      let ret = {}
      if (Object.getPrototypeOf(succ) !== Boolean.prototype) {
        ret = succ
      }
      checkPhoneIsRegister_true(ret);
    }
  })
}
//发送验证码
const checkPhoneIsRegister_true = async (ret) => {
  if (isCounting.value) return; // 防止重复发送
  let params = {
    phone: userInfo.value.phone,
    telephoneCode: "+63",
    type: getTypeInfo.value.msgType,
    geetest_guard: ret?.geetest_guard || '',
    userInfo: ret?.userInfo || '',
    geetest_captcha: ret?.geetest_captcha || '',
    buds: ret?.buds || '64',
  }
  const { code, msg } = await sendCodeMsg(params)
  if (code === 200) {
    showToast('Verification code sent successfully')
    hasSentCode.value = true;
    startCountdown(); // 启动倒计时
  } else {
    if (code === 600) {
      //TODO tipword23
    } else {
      msg && showToast(msg)
    }
  }
}

/**
 * 启动倒计时
 * 逻辑：60秒倒计时，结束后清除定时器
 */
const startCountdown = () => {
  if (isCounting.value) return;
  isCounting.value = true;
  count.value = 60;
  countdownTimer = setInterval(() => {
    count.value--;
    if (count.value <= 0) {
      clearInterval(countdownTimer);
      isCounting.value = false;
    }
  }, 1000);
};

const emit = defineEmits(["update:showDialog"]);

const handleCancel = () => {
  emit("update:showDialog", false);
}

const handleConfirm = async () => {
  if (verificationCode.value.length !== 6) {
    showToast("Code Error,Please Try Again");
    return;
  }
  handleCheckCode()
}


const handleCheckCode = async () => {
  if (verificationCode.value.length !== 6) {
    showToast("Code Error,Please Try Again");
    return;
  }
  const { code, msg } = await verifyCode({
    phone: userInfo.value.phone,
    telephoneCode: "+63",
    code: verificationCode.value,
    type: getTypeInfo.value.msgType
  })
  if (code === 200) {
    handleCancel()
    props.succCallBack && props.succCallBack()
  } else {
    showToast("Verification Error,Please Try Again");
  }
}


</script>

<style scoped lang="scss">
.dialog-content {
  padding-top: 12px;

  // 验证码步骤样式
  .send-code-tip {
    color: #222;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 16px;

    /* 171.429% */
  }

  .phone-input-code {
    label {
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-bottom: 6px;
      display: inline-block;
      margin-bottom: 12px;
    }

    .phone-input-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;

      input {
        flex: 1;
        height: 40px;
        border: 1px solid #eee;
        border-radius: 20px;
        padding: 0 12px;
        outline: none;
        background-color: #F4F7FD;
      }

      .get-code-btn {
        width: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        // padding: 12px 16px;
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
        background: #ac1140;
        color: #fff;
        text-align: center;
        font-size: 14px;
        cursor: pointer;

        &.is-counting {
          background: rgba(172, 17, 64, 0.5);
          cursor: not-allowed;
        }
      }
    }
  }
}

.phone-input {
  label {
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 6px;
    display: inline-block;
    margin-bottom: 12px;
  }

  input {
    flex: 1;
    height: 40px;
    border: 1px solid #eee;
    border-radius: 20px;
    padding: 0 12px;
    outline: none;
    background-color: #F4F7FD;
    width: 100%;
  }
}
</style>
