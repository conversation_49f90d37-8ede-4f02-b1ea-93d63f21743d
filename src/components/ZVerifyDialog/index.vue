<!-- 手机号验证通用弹窗 -->
<template>
  <div>
    <ZActionSheet v-model="visible" title="Verification" confirmText="Done" :showCancelButton="false"
      :onConfirm="handleConfirm" :onCancel="handleClose" :confirmDisabled="!isValidCode">
      <div class="dialog-content">
        <div class="send-code-tip" v-show="hasSentCode">
          A text message with a 6-digit code was just sent to
          <b>{{ formattedUserPhone }}</b>
        </div>
        <div class="phone-input-code">
          <label for="verCode">Enter a verification code</label>
          <div class="phone-input-container">
            <input id="verCode" v-model="verificationCode" maxlength="6" type="tel" placeholder="Enter the code"
              @input="handleCodeInput" />
            <ZButton class="get-code-btn" :disabled="isCounting" @click="checkPhoneIsRegister">
              {{ isCounting ? `${count}s` : "Get Code" }}
            </ZButton>
          </div>
        </div>
      </div>
    </ZActionSheet>
    <!-- 更改手机号 -->
    <template v-if="verifyType === PN_VERIFY_TYPE.ChangePhoneNumber">
      <VerifyDialogChangePhone v-model="showNextDialog" :verifyType="PN_VERIFY_TYPE.ChangePhoneNumber"
        @complete="showNextDialog = false">
      </VerifyDialogChangePhone>
    </template>
    <!-- 更新支付密码 -->
    <template v-if="verifyType === PN_VERIFY_TYPE.ChangePaymentPassword">
      <VerifyDialogPaymentPassword v-model="showNextDialog" :succCallBack="succCallBack"
        @complete="showNextDialog = false">
      </VerifyDialogPaymentPassword>
    </template>
    <!-- 更新登陆密码 -->
    <template v-if="verifyType === PN_VERIFY_TYPE.ForgetPassword">
      <VerifyDialogLoginPassword v-model="showNextDialog" :phone="passPhone" @complete="showNextDialog = false"
        :succCallBack="succCallBack"></VerifyDialogLoginPassword>
    </template>
  </div>
</template>

<script setup lang="ts">
import { sendCodeMsg, verifyCode, isBindSend } from "@/api/setPhoneNumber";
import { GeetestMgr, GEETEST_TYPE } from "@/utils/GeetestMgr";
import { useGlobalStore } from "@/stores/global";
import { ref, computed } from "vue";
import { showToast } from "vant";
import { PN_VERIFY_TYPE } from "./types";
import { GlobalEnum } from "@/utils/config/GlobalEnum";
import VerifyDialogChangePhone from "./VerifyDialogChangePhone.vue";
import VerifyDialogPaymentPassword from "./VerifyDialogPaymentPassword.vue";
import VerifyDialogLoginPassword from "./VerifyDialogLoginPassword.vue";

const globalStore = useGlobalStore();
const { userInfo } = storeToRefs(globalStore);

const props = defineProps({
  // 显示弹窗
  showDialog: {
    type: Boolean,
    default: false,
    required: false,
  },
  // 弹窗确认
  onConfirm: {
    type: Function,
    default: () => { },
    required: false,
  },
  // 弹窗关闭
  onCancel: {
    type: Function,
    default: () => { },
    required: false,
  },
  //  认证类型
  verifyType: {
    type: Number,
    default: -1,
    required: true,
  },
  // 传递的手机号，忘记密码场景
  passPhone: {
    type: String,
    default: "",
    required: false,
  },
  // 成功回调
  succCallBack: {
    type: Function,
    default: () => { },
    required: false,
  },
});

const showNextDialog = ref(false);

// 弹窗是否显示
const visible = ref(props.showDialog);
// 手机号
const phone = ref("");
// 原手机号
const oldPhone = ref("");
// 验证码
const verificationCode = ref("");
// 是否已发送验证码
const hasSentCode = ref(false);
// 是否正在倒计时
const isCounting = ref(false);
// 倒计时数值
const count = ref(60);

const smsType = ref(1);
// 倒计时定时器
let countdownTimer: NodeJS.Timeout | null = null;

// 验证码是否有效（6位数字）
const isValidCode = computed(() => {
  return verificationCode.value && verificationCode.value.length === 6;
});

// 处理验证码输入，只允许数字且限制6位
const handleCodeInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  let value = target.value;

  // 只保留数字
  value = value.replace(/\D/g, '');

  // 限制最多6位
  if (value.length > 6) {
    value = value.slice(0, 6);
  }

  verificationCode.value = value;
  target.value = value;
};
/**
 * 监听modelValue变化
 */
watch(
  () => props.showDialog,
  (val) => {
    visible.value = val;
    if (val) {
      init();
    } else {
      resetData();
    }
  }
);

const resetData = () => {
  phone.value = "";
  oldPhone.value = "";
  verificationCode.value = "";
  hasSentCode.value = false;
  isCounting.value = false;
  count.value = 60;
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
};

const init = () => {
  if ([PN_VERIFY_TYPE.SetPaymentPassword, PN_VERIFY_TYPE.ChangePaymentPassword].includes(props.verifyType)) {
    if (userInfo.value.phone) {
      phone.value = userInfo.value.phone;
    }
  } else if (props.verifyType == PN_VERIFY_TYPE.ForgetPassword) {
    phone.value = props.passPhone || userInfo.value.phone;
  } else if (props.verifyType == PN_VERIFY_TYPE.ChangePhoneNumber) {
    oldPhone.value = props.passPhone || userInfo.value.phone;
    phone.value = props.passPhone || userInfo.value.phone;
  }
  //TODO 忘记密码、添加提现账号、编辑提现账号 直接进行短信验证
};

//发送验证码之前先 geetest
const checkPhoneIsRegister = () => {
  // 调用方，一定要正确传 ‘verifyType’
  if (props.verifyType === -1) return;
  let gtype = "";
  switch (props.verifyType) {
    case PN_VERIFY_TYPE.ChangePhoneNumber:
      gtype = GEETEST_TYPE.change_pt_phone_code;
      break;
    case PN_VERIFY_TYPE.SetPhoneNumber:
      gtype = GEETEST_TYPE.bind_pt_phone_code;
      break;
    case PN_VERIFY_TYPE.AddWithdrawAccount:
      gtype = GEETEST_TYPE.bind_withdraw_account_code;
      break;
    case PN_VERIFY_TYPE.ChangeWithdrawAccount:
      gtype = GEETEST_TYPE.change_withdraw_account_code;
      break;
    case PN_VERIFY_TYPE.ForgetPassword:
      gtype = GEETEST_TYPE.forget_password_code;
      break;
    case PN_VERIFY_TYPE.ChangePaymentPassword:
      gtype = GEETEST_TYPE.change_pay_password_code;
      break;
    case PN_VERIFY_TYPE.SetPaymentPassword:
      gtype = GEETEST_TYPE.first_pay_password;
      break;
    case PN_VERIFY_TYPE.SetLoginPassword:
      gtype = GEETEST_TYPE.first_password;
      break;
    case PN_VERIFY_TYPE.ChangeLoginPassword:
      gtype = GEETEST_TYPE.forget_password_code;
      break;
    default:
      break;
  }
  GeetestMgr.instance.geetest_device(gtype, (succ) => {
    if (succ) {
      let ret = {};
      if (Object.getPrototypeOf(succ) !== Boolean.prototype) {
        ret = succ;
      }
      checkPhoneIsRegister_true(ret);
    }
  });
};
//发送验证码
const checkPhoneIsRegister_true = async (ret) => {
  if (isCounting.value) return; // 防止重复发送
  let params = {
    phone: phone.value,
    telephoneCode: "+63",
    type: props.verifyType,
    geetest_guard: ret?.geetest_guard || "",
    userInfo: ret?.userInfo || "",
    geetest_captcha: ret?.geetest_captcha || "",
    buds: ret?.buds || "64",
  };
  if (
    [
      PN_VERIFY_TYPE.SetPhoneNumber,
      PN_VERIFY_TYPE.SetPaymentPassword,
      PN_VERIFY_TYPE.ChangePhoneNumber,
      PN_VERIFY_TYPE.AddWithdrawAccount,
      PN_VERIFY_TYPE.ChangeWithdrawAccount,
      PN_VERIFY_TYPE.ChangePaymentPassword,
    ].includes(props.verifyType)
  ) {
    if (props.verifyType == PN_VERIFY_TYPE.AddWithdrawAccount) {
      params["type"] = GlobalEnum.SMS_TYPE.BIND_WITHDRAW_ACCOUNT;
    }
    if (props.verifyType == PN_VERIFY_TYPE.ChangeWithdrawAccount) {
      params["type"] = GlobalEnum.SMS_TYPE.UPDATE_WITHDRAW_ACCOUNT;
    }
    if (props.verifyType == PN_VERIFY_TYPE.ChangePhoneNumber) {
      params["type"] = GlobalEnum.SMS_TYPE.UPDATE_PHONE;
    }
    if (props.verifyType == PN_VERIFY_TYPE.SetPhoneNumber) {
      params["type"] = GlobalEnum.SMS_TYPE.BIND_PHONE;
    }
    if (props.verifyType == PN_VERIFY_TYPE.ChangePaymentPassword) {
      params["type"] = GlobalEnum.SMS_TYPE.SETTING_WALLET_PASSWORD;
    }
    if (props.verifyType == PN_VERIFY_TYPE.SetPaymentPassword) {
      params["type"] = GlobalEnum.SMS_TYPE.SETTING_WALLET_PASSWORD;
    }
    smsType.value = params["type"];

    const { code, msg } = await sendCodeMsg(params);

    if (code === 200) {
      showToast("Verification code sent successfully");
      hasSentCode.value = true;
      startCountdown(); // 启动倒计时
    } else {
      if (code === 600) {
        showToast("The Number is Linked to an Existing Account.",)
      } else {
        msg && showToast(msg);
      }
    }
  } else {
    //次数获取验证码既可以绑定手机号也可以修改密码
    let isForgetPw = props.verifyType == PN_VERIFY_TYPE.ForgetPassword;
    params["isBind"] = isForgetPw ? 1 : 0; //需要已经绑定填1  不需要绑定发0
    if (params["isBind"] === 0) {
      const { code, msg } = await isBindSend(params);
      if (code === 200) {
        showToast("Verification code sent successfully");
        hasSentCode.value = true;
        startCountdown(); // 启动倒计时
      } else {
        if (code === 600) {
          showToast("The Number is Linked to an Existing Account.",)
        } else {
          msg && showToast(msg);
        }
      }
    } else {
      smsType.value = GlobalEnum.SMS_TYPE.UPDATE_LOGIN_PASSWORD;
      const { code, msg } = await sendCodeMsg({
        ...params,
        type: GlobalEnum.SMS_TYPE.UPDATE_LOGIN_PASSWORD,
      });
      if (code === 200) {
        showToast("Verification code sent successfully");
        hasSentCode.value = true;
        startCountdown(); // 启动倒计时
      } else {
        if (code === 600) {
          showToast("The Number is Linked to an Existing Account.",)
        } else {
          msg && showToast(msg);
        }
      }
    }
  }
};

/**
 * 启动倒计时
 * 逻辑：60秒倒计时，结束后清除定时器
 */
const startCountdown = () => {
  if (isCounting.value) return;
  isCounting.value = true;
  count.value = 60;
  countdownTimer = setInterval(() => {
    count.value--;
    if (count.value <= 0) {
      clearInterval(countdownTimer);
      isCounting.value = false;
    }
  }, 1000);
};

const emit = defineEmits(["update:showDialog"]);

const handleClose = () => {
  emit("update:showDialog", false);
};

const formattedUserPhone = computed(() => {
  const phone = (userInfo.value.phone || props.passPhone || "").toString();
  return phone.slice(0, 2) + "****" + phone.slice(6, 10);
});

const handleConfirm = async () => {
  if (verificationCode.value.length !== 6) {
    showToast("Code Error,Please Try Again");
    return;
  }
  const { code, msg } = await verifyCode({
    phone: phone.value,
    telephoneCode: "+63",
    code: verificationCode.value,
    type: smsType.value,
  });
  if (code === 200) {
    showNextDialog.value = true;
    handleClose();
  } else {
    showToast("Verification Error,Please Try Again");
  }
};
</script>

<style scoped lang="scss">
.dialog-content {
  padding-top: 12px;

  // 验证码步骤样式
  .send-code-tip {
    color: #222;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 16px;

    /* 171.429% */
  }

  .phone-input-code {
    label {
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-bottom: 6px;
      display: inline-block;
      margin-bottom: 12px;
    }

    .phone-input-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;

      input {
        flex: 1;
        height: 40px;
        border: 1px solid #eee;
        border-radius: 20px;
        padding: 0 12px;
        outline: none;
        background-color: #f4f7fd;
      }

      .get-code-btn {
        width: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        // padding: 12px 16px;
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
        background: #ac1140;
        color: #fff;
        text-align: center;
        font-size: 14px;
        cursor: pointer;

        &.is-counting {
          background: rgba(172, 17, 64, 0.5);
          cursor: not-allowed;
        }
      }
    }
  }
}

.phone-input {
  label {
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 6px;
    display: inline-block;
    margin-bottom: 12px;
  }

  input {
    flex: 1;
    height: 40px;
    border: 1px solid #eee;
    border-radius: 20px;
    padding: 0 12px;
    outline: none;
    background-color: #f4f7fd;
    width: 100%;
  }
}
</style>
