/**
 * 游戏分类 Store
 * 管理游戏分类页面的状态和逻辑
 */

import { defineStore } from "pinia";
import { computed, reactive, ref } from "vue";
import { useGameStore } from "@/stores/game";
import { likeHistory, getGames } from "@/api/games";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import type {
  Game,
  GameCategory,
  PaginationState,
  GameCache,
  FilterState,
} from "@/views/game-categories/types";

// 游戏排序函数
const sortFunc = (a: any, b: any) => {
  if (parseInt(a.big_images_set) !== parseInt(b.big_images_set))
    return parseInt(b.big_images_set) - parseInt(a.big_images_set);
  if (parseInt(a.tags) === 0 && parseInt(b.tags) !== 0) return 1;
  if (parseInt(a.tags) !== 0 && parseInt(b.tags) === 0) return -1;
  //排序tags 1>3>2
  const aint = parseInt(a.tags);
  const bint = parseInt(b.tags);
  if (aint !== bint) {
    if (aint == 1) return -1;
    if (bint == 1) return 1;
    if (aint == 3) return -1;
    if (bint == 3) return 1;
  }
  if (parseInt(a.balance_record) !== parseInt(b.balance_record))
    return parseInt(b.balance_record) - parseInt(a.balance_record);
  if (parseInt(a.sort) !== parseInt(b.sort)) return parseInt(b.sort) - parseInt(a.sort);
  if (a.name && b.name) {
    return a.name.localeCompare(b.name);
  }
};

export const useGameCategoriesStore = defineStore("gameCategories", () => {
  const gameStore = useGameStore();

  // ==================== 状态定义 ====================

  // 分页状态
  const paginationState = reactive<PaginationState>({
    pageSize: 200,
    pageMap: {},
  });

  // 游戏缓存
  const gameCache = reactive<GameCache>({});

  // 强制更新触发器
  const forceUpdateTrigger = ref(0);

  // 已加载的tab记录
  const loadedTabs = reactive<Set<string | number>>(new Set());

  // 筛选状态
  const filterState = ref<FilterState>({
    searchValue: "",
    selectedCategories: ["all"],
  });

  // 加载状态
  const isDataLoading = ref(false);
  const isLoadingMore = ref(false);

  // 当前选中的tab索引
  const currentIndex = ref(0);

  // 筛选对话框可见状态
  const dialogVisible = ref(false);

  // ==================== 计算属性 ====================

  // 所有分类（包括基础分类、喜欢、历史分类）
  const allCategories = computed((): GameCategory[] => {
    const baseCategories = gameStore.gameTypes.map((category) => ({
      ...category,
      allGames: gameCache[String(category.id)] || [],
      pagedGames: [],
      hasMore: true,
    }));

    return [
      ...baseCategories,
      {
        id: "like",
        name: "Like",
        allGames: gameCache["like"] || [],
        pagedGames: [],
        hasMore: true,
      },
      {
        id: "history",
        name: "History",
        allGames: gameCache["history"] || [],
        pagedGames: [],
        hasMore: true,
      },
    ];
  });

  // 过滤后的分类
  const filteredCategories = computed(() => {
    forceUpdateTrigger.value;
    return allCategories.value.map((category) => {
      let filteredGames = category.allGames || [];
      // 先筛选厂商,注意 filterState.value.selectedCategories 是String 还是Number 类型
      if (
        filterState.value.selectedCategories.length &&
        !filterState.value.selectedCategories.includes("all")
      ) {
        filteredGames = filteredGames.filter(
          (game: Game) =>
            game.company_id !== undefined &&
            filterState.value.selectedCategories.includes(game.company_id)
        );
      }
      // 再筛选搜索
      if (filterState.value.searchValue) {
        const keyword = filterState.value.searchValue.toLowerCase();
        filteredGames = filteredGames.filter((game: Game) =>
          (game.name || "").toLowerCase().includes(keyword)
        );
      }

      // 过滤掉隐藏的游戏ID
      filteredGames = filteredGames.filter((game: Game) => !gameStore.hideList.includes(game.id));

      // History 只取前50条数据
      if (category.id === "history") {
        filteredGames = filteredGames.slice(0, 50);
      }

      // 喜欢、历史 不需要大图排版
      if (!["history", "like"].includes(category.id)) {
        // 分离大图和小图游戏
        const bigGames: Game[] = [];
        const normalGames = filteredGames.filter((game: any) => {
          if (game.big_images_set !== 0) {
            bigGames.push(game);
            return false;
          }
          return true;
        });
        // 大图排序
        bigGames.sort(sortFunc);
        // 在大图与小图之间添加分隔标记
        if (bigGames.length % 2 !== 0) {
          bigGames.push({
            id: `lineBreak_${category.id}`,
            game_id: `lineBreak_${category.id}`,
          } as Game);
        }
        // 小图游戏排序
        normalGames.sort(sortFunc);
        filteredGames = [...bigGames, ...normalGames];
      }

      // 分页
      const page = paginationState.pageMap[category.id] || 1;
      const pagedGames = filteredGames.slice(0, page * paginationState.pageSize);

      return {
        ...category,
        allGames: filteredGames,
        pagedGames,
        hasMore: filteredGames.length > pagedGames.length,
      };
    });
  });

  // 是否有筛选条件
  const hasFilters = computed(() => {
    return (
      filterState.value.selectedCategories.length > 0 &&
      !filterState.value.selectedCategories.includes("all")
    );
  });

  // ==================== 方法定义 ====================

  // 处理搜索
  const handleSearch = (value: string) => {
    filterState.value.searchValue = value;
  };

  // 清除筛选条件
  const handleClearFilters = () => {
    filterState.value.selectedCategories = ["all"];
  };

  // 确认筛选条件
  const handleConfirmFilters = (categories: string[] = []) => {
    filterState.value.selectedCategories = categories;
  };

  // 根据分类类型获取游戏数据
  const fetchGamesByType = async (type: string | number): Promise<void> => {
    const cacheKey = String(type);
    if (gameCache[cacheKey]) {
      return; // 已有缓存，直接返回
    }
    try {
      const res = await getGames({ type, is_new: 1 });
      const gameData = res?.data || res || [];
      gameCache[cacheKey] = gameData;
    } catch (error) {
      console.error(`Failed to fetch games for type ${type}:`, error);
      gameCache[cacheKey] = [];
    }
  };

  // 获取喜欢或历史记录的游戏数据
  const fetchLikeHistory = async (type: 1 | 2): Promise<void> => {
    try {
      const res = await likeHistory({ type });
      const gameData = res?.data || res || [];
      const cacheKey = type === 1 ? "like" : "history";
      gameCache[cacheKey] = gameData;
    } catch (error) {
      console.error(`Failed to fetch ${type === 1 ? "like" : "history"} list:`, error);
      const cacheKey = type === 1 ? "like" : "history";
      gameCache[cacheKey] = [];
    }
  };

  // 处理tab切换
  const handleTabChange = async (index: number): Promise<void> => {
    currentIndex.value = index;
    const category = filteredCategories.value[index];

    if (!category) return;

    // History 标签页不缓存，每次都重新请求
    const isHistoryTab = category.id === "history";

    // 如果该 tab 已经加载过且不是 history 标签页，直接返回
    if (loadedTabs.has(category.id) && !isHistoryTab) {
      if (category.pagedGames && category.pagedGames.length > 0) {
        return;
      } else {
        // 数据丢失，重新标记为未加载
        loadedTabs.delete(category.id);
      }
    }

    // 设置加载状态
    isDataLoading.value = true;
    showZLoading();

    try {
      if (category.id === "like") {
        await fetchLikeHistory(1);
      } else if (category.id === "history") {
        // History 标签页每次都清除缓存并重新请求
        delete gameCache["history"];
        await fetchLikeHistory(2);
      } else {
        await fetchGamesByType(category.id);
      }

      // 标记该 tab 已加载（history 除外）
      if (!isHistoryTab) {
        loadedTabs.add(category.id);
      }
    } finally {
      isDataLoading.value = false;
      closeZLoading();
    }
  };

  // 加载更多游戏数据
  const loadMoreGames = async (categoryId: string | number): Promise<void> => {
    isLoadingMore.value = true;
    showZLoading();

    try {
      await new Promise((resolve) => setTimeout(resolve, 200));
      const currentPage = paginationState.pageMap[categoryId] || 1;
      paginationState.pageMap[categoryId] = currentPage + 1;
    } finally {
      closeZLoading();
      isLoadingMore.value = false;
    }
  };

  // 更新游戏喜欢状态
  const handleUpdateLike = async (updatedGame: Game): Promise<void> => {
    try {
      // 更新缓存中的游戏状态
      Object.keys(gameCache).forEach((key) => {
        const games = gameCache[key];
        const index = games.findIndex(
          (game) => game.game_id === updatedGame.game_id || game.id === updatedGame.id
        );
        if (index !== -1) {
          games[index].is_like = updatedGame.is_like;
        }
      });

      // 强制触发响应式更新
      forceUpdateTrigger.value++;

      // 更新喜欢列表
      await fetchLikeHistory(1);
    } catch (error) {
      console.error("Failed to update like status:", error);
    }
  };

  // 重置状态
  const resetState = () => {
    Object.keys(gameCache).forEach((key) => {
      delete gameCache[key];
    });
    filterState.value = {
      searchValue: "",
      selectedCategories: ["all"],
    };
    loadedTabs.clear();
    currentIndex.value = 0;
  };

  return {
    // 状态
    paginationState,
    gameCache,
    forceUpdateTrigger,
    loadedTabs,
    filterState,
    isDataLoading,
    isLoadingMore,
    currentIndex,
    dialogVisible,

    // 计算属性
    allCategories,
    filteredCategories,
    hasFilters,

    // 方法
    handleSearch,
    handleClearFilters,
    handleConfirmFilters,
    fetchGamesByType,
    fetchLikeHistory,
    handleTabChange,
    loadMoreGames,
    handleUpdateLike,
    resetState,
  };
});
