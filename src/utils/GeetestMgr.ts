// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { ALL_APP_SOURCE_CONFIG } from "./config/Config";
import { E_CHANEL_TYPE, CHANEL_TERMINAL } from "./config/GlobalConstant";
import { showToast } from "vant";
import { getToken } from "@/utils/auth";
import { Md5 } from "./core/Md5";
import Request from "@/utils/http";
import { useGlobalStore } from "@/stores/global";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
//geetest 验证场景类型
export const GEETEST_TYPE = {
  password_login: "password_login",
  phone_code_login: "phone_code_login",
  google_facebook_login: "google_facebook_login",
  forget_password: "forget_password",
  first_password: "first_password",
  first_pay_password: "first_pay_password",
  change_pay_password: "change_pay_password",
  bind_withdraw_account: "bind_withdraw_account",
  change_withdraw_account: "change_withdraw_account",
  withdraw: "withdraw",
  bind_pt_phone: "bind_pt_phone",
  change_pt_phone: "change_pt_phone",
  phone_login_code: "phone_login_code", //登陆 get 验证码
  forget_password_code: "forget_password_code",
  change_pay_password_code: "change_pay_password_code",
  bind_withdraw_account_code: "bind_withdraw_account_code",
  change_withdraw_account_code: "change_withdraw_account_code",
  bind_pt_phone_code: "bind_pt_phone_code",
  change_pt_phone_code: "change_pt_phone_code",
};
export class GeetestMgr {
  private geeType: string = ""; //当前验证 类型
  private call_back; //用来通知 返回的结果
  private requstType: string = "get"; //
  private this_geetest_id: string = ""; //这次验证的id
  private geetoken: string = ""; //这是geetest 返回给我的
  private allids_arr: string[] = []; //请求配置之后 保存所有id
  private random_str: string = ""; //用于随机字符串 两个服务器对照使用
  private set_func_time: NodeJS.Timeout | null = null; //用于 倒计时弹窗的提示
  private is_load_all_ids: boolean = false; //是否加载过 所有ids
  static _instance: GeetestMgr;
  static get instance() {
    if (this._instance) {
      return this._instance;
    }
    this._instance = new GeetestMgr();
    return this._instance;
  }
  private randomString(e: number) {
    let ee = e || 32;
    let t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
    let a = t.length;
    let n = "";
    for (let index = 0; index < ee; index++) {
      n += t.charAt(Math.floor(Math.random() * a));
    }
    return n;
  }

  /**
   * 根据当前渠道获取对应的终端值
   * 统一管理所有终端值的获取逻辑，避免重复代码
   * @returns 终端值字符串
   */
  private getTerminalByChannel(): string {
    const globalStore = useGlobalStore();
    const currentChannel = globalStore.channel;
    ALL_APP_SOURCE_CONFIG.channel = currentChannel;

    // 根据渠道返回对应的终端值
    switch (currentChannel) {
      case E_CHANEL_TYPE.MAYA:
        return "64";
      case E_CHANEL_TYPE.G_CASH:
        return "64";
      case E_CHANEL_TYPE.H5:
      case E_CHANEL_TYPE.WEB:
      default:
        return "128"; // 默认值，适用于 H5、WEB 等渠道
    }
  }
  /**
   * 获取对应的 geetest id
   * @returns 验证场景对应的 geetest id，如果未找到返回空字符串
   */
  private get_geetest_id(): string {
    const terminal = this.getTerminalByChannel();

    // 查找匹配的验证场景
    const matchedScene = this.allids_arr.find((element: any) => {
      return String(element.terminal) === terminal && element.scene === this.geeType;
    });

    const geetestId = (matchedScene as any)?.id || "";

    if (!geetestId) {
      console.warn(`⚠️ 未找到匹配的 Geetest ID: terminal=${terminal}, scene=${this.geeType}`);
    }

    return geetestId;
  }
  //倒计时 是否超过5秒 没有弹出geetest
  geetestTimeout() {
    let self = this;
    this.set_func_time = setTimeout(() => {
      self.set_func_time = null;
    }, 60000);
  }
  //取消倒计时
  removeTimeout() {
    if (this.set_func_time) {
      clearTimeout(this.set_func_time);
      this.set_func_time = null;
    }
  }
  /**
   * 设备验证第一步
   * @param gee_type 验证类型
   * @param callBack 回调函数
   * @param phone 手机号（可选）
   * @param call2 第二个回调函数（可选）
   */
  public async geetest_device(
    gee_type: string,
    callBack?: any,
    phone?: string,
    call2?: any
  ): Promise<void> {
    try {
      this.geeType = gee_type;
      this.this_geetest_id = this.get_geetest_id();

      // 如果没有找到对应的验证ID，直接返回成功（跳过验证）
      if (!this.this_geetest_id) {
        console.log(`ℹ️ 跳过验证: ${gee_type} (未配置验证场景)`);
        return await callBack?.(true);
      }

      // 检查 Geetest 库是否可用
      if (!window["initGeeGuard"]) {
        console.error("❌ Geetest 库未加载");
        return await callBack?.(false);
      }

      this.geetestTimeout();
      // 显示 loading
      showZLoading({
        forbidClick: true,
        showCloseButton: false, // 验证过程中不显示关闭按钮
        backgroundColor: "rgba(0, 0, 0, 0.7)",
      });
      this.random_str = phone || this.randomString(20);
      this.call_back = callBack;

      const self = this;
      window["initGeeGuard"](
        { appId: ALL_APP_SOURCE_CONFIG.geetest_key },
        async function (data: any) {
          closeZLoading();
          // 获取到gee_token处理业务逻辑
          if (data.status === "success") {
            self.geetoken = data.data.gee_token;
            self.geetest_slide(call2);
          } else {
            showToast(data.data.msg);
            // 验证失败时关闭可能的 loading
            await callBack?.(false);
          }
        }
      );
    } catch (error) {
      console.error("❌ Geetest 设备验证失败:", error);
      this.removeTimeout();
      // 确保关闭可能的 loading
      closeZLoading();
      await callBack?.(false);
    }
  }
  private async send_token_server(result?: any) {
    let params: any = {};
    params["token"] = getToken();
    params["type"] = this.geeType;
    let guard = JSON.stringify({
      app_id: ALL_APP_SOURCE_CONFIG.geetest_key,
      gee_token: this.geetoken,
    });
    params["geetest_guard"] = guard;
    params["userInfo"] = Md5.hashStr(this.random_str || "default").toString();
    if (result) {
      params["geetest_captcha"] = JSON.stringify(result);
    }
    params["buds"] = this.getTerminalByChannel();

    try {
      await this.call_back?.(params);
    } catch (error) {
      console.error("❌ Geetest 回调执行失败:", error);
    } finally {
      // 无论成功失败都关闭 loading
      closeZLoading();

      let self = this;
      setTimeout(() => {
        self.removeTimeout();
      }, 1000);
    }
  }
  //开始滑动验证 第二步
  private geetest_slide(call2?: any) {
    //提现的时候 先设备验证
    let self = this;
    window["initGeetest4"](
      {
        // 省略必须的配置参数
        lang: "eng",
        product: "bind",
        userInfo: Md5.hashStr(this.random_str || "default").toString(), //这里上传 手机号md5 加密
        captchaId: this.this_geetest_id,
      },
      function (captchaObj: any) {
        captchaObj
          .onReady(function () {
            call2 && call2(); //通知一下登录 使用防止多次点击
            if (self.set_func_time != null) {
              captchaObj.showCaptcha();
              self.removeTimeout();
            }
          })
          .onSuccess(function () {
            let result = captchaObj.getValidate();
            self.send_token_server(result);
            // 结合业务逻辑，判断是否需要移除验证
            captchaObj.destroy();
            captchaObj = null;
          })
          .onError(function () {
            console.log("----------geetest verify failed");
            // 验证失败时关闭 loading
            closeZLoading();
          });
      }
    );
  }
  /**
   * 检查 Geetest JS 库是否已加载
   */
  private checkGeetestLibraries(): boolean {
    return !!(window["initGeeGuard"] && window["initGeetest4"]);
  }

  /**
   * 等待 Geetest JS 库加载完成
   */
  private async waitForGeetestLibraries(timeout = 10000): Promise<boolean> {
    const startTime = Date.now();

    while (!this.checkGeetestLibraries()) {
      if (Date.now() - startTime > timeout) {
        console.error("❌ Geetest 库加载超时");
        return false;
      }
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    console.log("✅ Geetest 库加载完成");
    return true;
  }
  /**
   * 获取所有的验证 geeids
   * 注意：此方法应该在 preLogin 之后调用，确保 globalStore.channel 已正确设置
   */
  public async get_all_geetest_ids(): Promise<void> {
    if (this.is_load_all_ids) {
      return Promise.resolve();
    }

    this.is_load_all_ids = true;
    const buds = this.getTerminalByChannel();
    const params = { buds };

    return Request.post("/common/api/geetest/risk/config", params)
      .then((response: any) => {
        const responseData = response?.data || response;

        // 验证响应数据结构
        if (!responseData?.verify_app || !Array.isArray(responseData.verify_app)) {
          throw new Error("Invalid response: verify_app not found or not an array");
        }

        if (!responseData?.verify_scene || !Array.isArray(responseData.verify_scene)) {
          throw new Error("Invalid response: verify_scene not found or not an array");
        }

        // 设置 geetest key
        const appids = responseData.verify_app;
        if (appids.length > 0 && appids[0]?.id) {
          ALL_APP_SOURCE_CONFIG.geetest_key = appids[0].id;
        } else {
          throw new Error("No valid geetest key found in response");
        }

        // 设置验证场景数组
        this.allids_arr = responseData.verify_scene;

        console.log(`✅ Geetest 配置加载成功: ${this.allids_arr.length} 个验证场景`);
      })
      .catch((error) => {
        console.error("❌ Geetest 配置加载失败:", error);
        this.is_load_all_ids = false; // 重置状态，允许重试
        throw error; // 重新抛出错误，让调用方处理
      });
  }

  /**
   * 初始化 Geetest（在渠道确定后调用）
   * 应该在 loginManager.preLogin() 之后调用
   */
  public async initializeAfterChannelSet(): Promise<void> {
    try {
      console.log("🚀 开始初始化 Geetest...");

      // 1. 等待 JS 库加载完成（已在 index.html 中异步加载）
      const librariesLoaded = await this.waitForGeetestLibraries();
      if (!librariesLoaded) {
        throw new Error("Geetest 库加载超时");
      }

      // 2. 获取配置（现在渠道已确定）
      await this.get_all_geetest_ids();

      console.log("✅ Geetest 初始化完成");
    } catch (error) {
      console.error("❌ Geetest 初始化失败:", error);
      throw error; // 重新抛出错误，让调用方知道初始化失败
    }
  }
}
