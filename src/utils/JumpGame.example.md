# JumpGame 使用指南

## 简化的 API

JumpGame 现在提供了简化的 API，自动处理移动端兼容性，无需传递额外参数。

### 基本用法

```typescript
import { jumpGame } from "@/utils/JumpGame";

// 简单调用，自动处理移动端兼容性
jumpGame(gameData);
```

### 游戏数据格式

```typescript
interface GameData {
  id: number;           // 游戏 ID
  company_id?: string;  // 公司 ID
  third_party_game_id?: string; // 第三方游戏 ID
  [key: string]: any;   // 其他属性
}
```

## 使用场景

### 1. 游戏列表点击

```vue
<template>
  <div class="game-list">
    <div 
      v-for="game in gameList" 
      :key="game.id"
      class="game-item"
      @click="() => jumpGame(game)"
    >
      <img :src="game.image" />
      <div class="name">{{ game.name }}</div>
    </div>
  </div>
</template>

<script setup>
import { jumpGame } from "@/utils/JumpGame";
// 直接在模板中使用，无需额外处理
</script>
```

### 2. 游戏卡片组件

```vue
<template>
  <div class="game-card" @click="handleClick">
    <!-- 游戏内容 -->
  </div>
</template>

<script setup>
import { jumpGame } from "@/utils/JumpGame";

const props = defineProps({
  game: Object
});

const handleClick = () => {
  // 简单调用，自动处理移动端兼容性
  jumpGame(props.game);
};
</script>
```

### 3. 推荐游戏跳转

```typescript
const jumpToRecommendedGame = (gameId) => {
  // 只有 ID 的情况，JumpGame 会自动获取完整游戏信息
  jumpGame({ id: gameId });
};
```

## 内部工作流程

### 1. 内跳游戏（应用内路由）

```
用户点击游戏 → KYC 验证 → 获取游戏信息 → 路由跳转到游戏页面
```

### 2. 外跳游戏（新窗口打开）

```
用户点击游戏 → KYC 验证 → 获取游戏信息 → 智能打开新窗口 → 游戏启动
```

## 移动端适配

JumpGame 现在使用 MobileWindowManager 自动处理移动端兼容性：

```typescript
// 内部自动处理：
// 1. 检测是否为移动端
// 2. 预判断是否需要外跳
// 3. 需要外跳时预打开窗口
// 4. 智能回退机制保证功能可用
```

### 智能预打开策略

```typescript
// 只在确定需要外跳且为移动端时才预打开窗口
if (isJump && MobileWindowManager.isMobile()) {
  preOpenedWindow = MobileWindowManager.preOpenWindow();
}
```

### 动态窗口管理

```typescript
// 如果 API 响应包含 game_html，也需要外跳
if (response.game_html) {
  go_handle = true;
  // 如果之前没有预打开窗口，现在预打开
  if (!preOpenedWindow && MobileWindowManager.isMobile()) {
    preOpenedWindow = MobileWindowManager.preOpenWindow();
  }
}
```

## 错误处理

JumpGame 内部自动处理各种错误情况：

```typescript
try {
  // 游戏启动逻辑
} catch (err) {
  closeToast();
  showToast("The game cannot be accessed, please contact customer service.");
  // 自动清理窗口
  MobileWindowManager.closeWindow(preOpenedWindow);
}
```

## 与 JumpPromo 的一致性

JumpGame 和 JumpPromo 现在使用相同的简化 API 模式：

### JumpGame
```typescript
// 简单调用
jumpGame(gameData);
```

### JumpPromo
```typescript
// 简单调用
jumpPromo(promoItem);
jumpBanner(bannerItem);
jumpPromosItem(promoItem);
```

## 兼容性保证

### 桌面端
- ✅ **外跳**：直接使用 `window.open()`，体验不变
- ✅ **内跳**：正常路由跳转

### 移动端
- ✅ **外跳**：智能预打开窗口，确保游戏能正常启动
- ✅ **内跳**：正常路由跳转
- ✅ **回退机制**：多层回退保证功能可用

## 性能优化

### 最小化开销
- ✅ **按需创建**：只在需要外跳时才预打开窗口
- ✅ **智能检测**：只在移动端进行特殊处理
- ✅ **快速回退**：失败时立即使用备选方案

### 内存管理
- ✅ **自动清理**：失败的窗口会自动关闭
- ✅ **无泄漏**：不保留不必要的窗口引用

## 总结

通过这次优化，我们实现了：

1. **简化 API**：无需传递复杂的参数
2. **自动适配**：内部自动处理移动端兼容性
3. **智能预打开**：只在需要时才预打开窗口
4. **错误处理**：完善的错误恢复机制
5. **一致性**：与 JumpPromo 保持相同的 API 风格

现在你可以简单地调用 `jumpGame(game)` 就能在所有平台上正常启动游戏！🎮
