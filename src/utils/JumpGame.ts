import { showToast } from "vant";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import { getOS } from "@/utils/core/tools";
import { getBitValue } from "@/utils/core/tools";
import { useGlobalStore } from "@/stores/global";
import { E_CHANEL_TYPE, MAINTENANCETIPCODE } from "@/utils/config/GlobalConstant";
import { gameItemLogin, getGameInfo } from "@/api/games";
import { getGlobalDialog } from "@/enter/vant";
import { KycMgr, InGameType } from "@/utils/KycMgr";
import { getToken } from "@/utils/auth";
import router from "@/router";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";

const $dialog = getGlobalDialog();
const globalStore = useGlobalStore();

const showMaintenancetip = (response: any) => {
  $dialog({
    title: "Maintenance",
    message: response.msg,
    confirmText: "Done",
    showCancelButton: false,
    onConfirm() {
      const currentRoute = router.currentRoute.value;
      if (currentRoute.path === "/game-webview") {
        router.back();
      }
    },
  });
};

//用二进制的值做的对应
export enum OS_JumpOpen {
  android_h5 = 1,
  android_app,
  android_maya,
  ios_h5,
  ios_app,
  ios_maya,
}

// 异常处理
export const handleException = (response: any) => {
  if (!response) return false;
  if (response.code == MAINTENANCETIPCODE || response.code == 102041) {
    showMaintenancetip(response);
    return false;
  } else if (response.code == 102046) {
    showToast("Sorry, the current game does not exist");
    return false;
  } else if (response.code == 102010) {
    $dialog({
      message: "Invalid access token. Please login again.",
      confirmText: "Done",
      showCancelButton: false,
      onConfirm() {
        // 退出登陆
        globalStore.loginOut();
      },
    });
    return false;
  } else if ([1200, 101013, 9].includes(response.code)) {
    //kyc没有审核通过 提示Please perform KYC verification first
    showToast(response.msg);
    return false;
  } else if (response.code == 500 || response.code == 401) {
    showToast("The game cannot be accessed, please contact customer service.");
    return false;
  }
  return true;
};

const goNext = async (data: any) => {
  // 先关闭可能残留的 loading
  closeZLoading();
  // 确保 showZLoading 能正常挂载
  showZLoading({
    duration: 0,
  });

  let gameInfo = data;
  // 其它入口
  if (!data.company_id || !data.third_party_game_id) {
    const gameInfoResponse = await getGameInfo({ id: data.id });
    const gameInfoList = gameInfoResponse.data || gameInfoResponse;
    if (gameInfoList && gameInfoList?.length > 0) {
      gameInfo = gameInfoList[0];
    } else {
      closeZLoading();
      return;
    }
  }

  let { is_jump, id, third_party_game_id, company_id } = gameInfo;
  // 这块取数有疑问
  let isJump = is_jump ? getGameJumpType(is_jump) : getGameJumpType(is_jump);

  // 初始化预打开窗口变量，但暂时不打开窗口
  let preOpenedWindow: Window | null = null;

  let params: any;
  if (id) {
    params = {
      game_id: id,
      company_id: company_id,
    };
  } else {
    params = {
      third_game_id: third_party_game_id,
      company_id: company_id,
    };
  }

  try {
    const apiResponse = await gameItemLogin(params);
    // 提取实际的响应数据
    const response = apiResponse.data || apiResponse;
    closeZLoading();
    const isPass = handleException(response);
    if (!isPass) {
      return;
    }

    let go_handle = false;
    if (isJump) {
      go_handle = true;
    }
    if (response.game_html) {
      go_handle = true; //类似pg游戏 直接走外跳逻辑
    }

    // 如果需要外跳但没有有效的游戏 URL 或 HTML，直接返回
    if (go_handle && !response.game_url && !response.game_html) {
      showToast(response.msg || "The game cannot be accessed, please contact customer service.");
      return;
    }

    // 接口请求成功且验证通过后，如果需要外跳且是移动端，才预打开窗口
    if (go_handle && MobileWindowManager.isMobile()) {
      preOpenedWindow = MobileWindowManager.preOpenWindow();
      if (!preOpenedWindow) {
        console.warn("Popup blocked, will fallback to current window navigation");
      }
    }
    if (go_handle) {
      // 外跳 - 需要打开新窗口
      handleJumpOpen(response, preOpenedWindow);
    } else {
      // 内跳 - 只是路由跳转，关闭预打开的窗口（如果有）
      MobileWindowManager.closeWindow(preOpenedWindow);
      const { third_game_id, company_id, game_id } = params;
      router.push({
        path: "/game-webview",
        query: {
          third_game_id,
          company_id,
          id: game_id,
        },
      });
    }
  } catch (err) {
    closeZLoading();
    showToast("The game cannot be accessed, please contact customer service.");
    // 如果有预打开的窗口，关闭它
    MobileWindowManager.closeWindow(preOpenedWindow);
  }
};

/**
 * 游戏跳转方法
 * 自动处理移动端兼容性，无需传递额外参数
 * @param data 游戏数据
 */
export const jumpGame = async (data: { id: number; [key: string]: any }) => {
  if (!getToken()) {
    router.push("/login");
    return;
  }

  //这里拦截一下 验证是否完整版 kyc 验证
  KycMgr.instance.verifyKyc(InGameType.GoThirdGame, (isVerity) => {
    if (isVerity) {
      goNext(data);
    }
  });
};

const handleJumpOpen = (response: any, preOpenedWindow: Window | null = null) => {
  if (!response) return;

  if (response.game_url) {
    // 使用 MobileWindowManager 进行安全的 URL 导航
    // 传递预打开的窗口以获得更好的移动端体验
    const success = MobileWindowManager.navigateToUrl(response.game_url, preOpenedWindow);

    if (!success) {
      console.error("Failed to navigate to game URL:", response.game_url);
      showToast("Failed to open game, please try again");
    }
  } else if (response.game_html) {
    // 对于 HTML 内容，关闭预打开的窗口（如果有）并在当前窗口显示
    MobileWindowManager.closeWindow(preOpenedWindow);

    window.history.pushState({ page: window.location.href }, "Nustar", window.location.href);
    window.document.open();
    window.document.write(response.game_html);
    window.document.close();
  }
};

const getGameJumpType = (isJump: number) => {
  if (!isJump) return false;
  const platom = getOS();
  const globalStore = useGlobalStore();
  if (platom === "android") {
    if (globalStore.channel === E_CHANEL_TYPE.MAYA) {
      return getBitValue(isJump, OS_JumpOpen.android_maya) == 1;
    } else {
      return getBitValue(isJump, OS_JumpOpen.android_h5) == 1;
    }
  } else if (platom === "ios") {
    if (globalStore.channel === E_CHANEL_TYPE.MAYA) {
      return getBitValue(isJump, OS_JumpOpen.ios_maya) == 1;
    } else {
      return getBitValue(isJump, OS_JumpOpen.ios_h5) == 1;
    }
  } else {
    return getBitValue(isJump, OS_JumpOpen.android_h5) == 1;
  }
};
