# JumpPromo 移动端适配说明

## 简化后的使用方式

### ✅ **现在的使用方式（简洁）**

```typescript
import { jumpPromo, jumpBanner, jumpPromosItem } from "@/utils/JumpPromo";

// 1. 基本使用 - 无需传递额外参数
jumpPromo(promoItem);

// 2. Banner 跳转
jumpBanner(bannerItem);

// 3. Promos 跳转
jumpPromosItem(promoItem);
```

### ❌ **之前的复杂方式（已简化）**

```typescript
// 不再需要这样复杂的调用
const preOpenedWindow = MobileWindowManager.preOpenWindow();
jumpPromo(promoItem, "promos", preOpenedWindow);
```

## 自动移动端适配

### 内部自动处理逻辑

```typescript
// JumpPromo 内部会自动：
if (item.jump_type === "2") { // 外跳
  // 1. 检测是否为移动端
  // 2. 自动尝试创建窗口（如果在用户事件中）
  // 3. 失败时自动回退到当前窗口导航
  MobileWindowManager.navigateToUrl(item.url);
}
```

### MobileWindowManager 增强

```typescript
static navigateToUrl(url: string, preOpenedWindow?: Window | null): boolean {
  // 如果是移动端且没有预打开窗口，自动尝试创建
  if (this.isMobile() && !preOpenedWindow) {
    try {
      const autoWindow = window.open("about:blank", "_blank");
      if (autoWindow) {
        preOpenedWindow = autoWindow;
      }
    } catch (error) {
      // 自动回退处理
    }
  }
  
  // 后续处理...
}
```

## 使用场景示例

### 1. Banner 点击

```vue
<template>
  <div @click="handleBannerClick">
    <img :src="banner.image" />
  </div>
</template>

<script setup>
import { jumpBanner } from "@/utils/JumpPromo";

const handleBannerClick = () => {
  // 简单调用，自动处理移动端兼容性
  jumpBanner(banner);
};
</script>
```

### 2. Promo 列表项点击

```vue
<template>
  <div 
    v-for="promo in promoList" 
    :key="promo.id"
    @click="() => jumpPromosItem(promo)"
  >
    {{ promo.title }}
  </div>
</template>

<script setup>
import { jumpPromosItem } from "@/utils/JumpPromo";
// 直接在模板中使用，无需额外处理
</script>
```

### 3. 自定义跳转逻辑

```typescript
const handleCustomJump = (item: PromoItem) => {
  // 可以添加自定义逻辑
  if (item.needsLogin && !isLoggedIn()) {
    router.push("/login");
    return;
  }
  
  // 然后简单调用跳转
  jumpPromo(item, "custom");
};
```

## 跳转类型处理

### 内跳（jump_type: "1"）
```typescript
// 自动路由跳转，无需窗口管理
router.push({ path: "/promo-webview", query: { url: item.url } });
```

### 外跳（jump_type: "2"）
```typescript
// 自动移动端适配
MobileWindowManager.navigateToUrl(item.url);
// 内部处理：
// - 桌面端：直接 window.open()
// - 移动端：尝试自动创建窗口，失败时当前窗口跳转
```

### 固定活动（jump_type: "4"）
```typescript
// 内部路由跳转
router.push({ path: `/promos/promo_${item.activity_list}` });
```

### 自定义活动（jump_type: "7"）
```typescript
// 内部路由跳转
router.push({ path: `/promos/promo_0`, query: { detail: JSON.stringify(item) } });
```

## 兼容性保证

### 桌面端
- ✅ **外跳**：直接使用 `window.open()`，体验不变
- ✅ **内跳**：正常路由跳转

### 移动端
- ✅ **外跳**：自动尝试创建窗口，失败时当前窗口跳转
- ✅ **内跳**：正常路由跳转
- ✅ **回退机制**：多层回退保证功能可用

### 浏览器支持
- ✅ iOS Safari 12+
- ✅ Android Chrome 70+
- ✅ 微信内置浏览器
- ✅ 其他主流移动端浏览器

## 错误处理

### 自动错误恢复
```typescript
// 内部自动处理各种错误情况：
// 1. 窗口创建失败 → 直接 window.open()
// 2. window.open() 失败 → 当前窗口跳转（移动端）
// 3. URL 无效 → 控制台警告，不执行跳转
```

### 调试信息
```typescript
// 开发环境会输出详细的调试信息
console.warn("Failed to auto-create window:", error);
console.warn("Falling back to current window navigation");
```

## 性能优化

### 最小化开销
- ✅ **按需创建**：只在需要外跳时才尝试创建窗口
- ✅ **智能检测**：只在移动端进行特殊处理
- ✅ **快速回退**：失败时立即使用备选方案

### 内存管理
- ✅ **自动清理**：失败的窗口会自动关闭
- ✅ **无泄漏**：不保留不必要的窗口引用

## 迁移指南

### 从旧版本迁移

```typescript
// 旧代码（需要手动处理）
const preWindow = MobileWindowManager.preOpenWindow();
jumpPromo(item, "promos", preWindow);

// 新代码（自动处理）
jumpPromo(item, "promos");
```

### 无需修改的场景
- ✅ 所有现有的 `jumpPromo()` 调用都无需修改
- ✅ 所有现有的 `jumpBanner()` 调用都无需修改
- ✅ 所有现有的 `jumpPromosItem()` 调用都无需修改

## 总结

通过这次优化，我们实现了：

1. **简化 API**：无需传递复杂的 `preOpenedWindow` 参数
2. **自动适配**：内部自动处理移动端兼容性
3. **向后兼容**：现有代码无需修改
4. **智能回退**：多层回退机制保证功能可用
5. **性能优化**：按需处理，最小化开销

现在你可以简单地调用 `jumpPromo(item)` 就能在所有平台上正常工作！🎉
