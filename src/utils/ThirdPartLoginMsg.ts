import { showToast } from "vant";
import { useLoginStore } from "@/stores/login";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";

// https://juejin.cn/post/7264168916291960869?searchId=20250728135422DE42FFB874461714BEF2
// https://juejin.cn/post/7247044************

export class LoginMgr {
  static _instance: LoginMgr;
  static get instance() {
    if (this._instance) {
      return this._instance;
    }
    this._instance = new LoginMgr();
    return this._instance;
  }
  //获取重定向 链接根据环境
  private get_redirect_uri() {
    return "urn:ietf:wg:oauth:2.0:oob:auto";
    return import.meta.env.VITE_WEB_URL;
  }
  //google 登录初始化
  public google_init() {
    var script = document.createElement("script");
    script.src = "https://accounts.google.com/gsi/client";
    script.async = true;
    document.head.appendChild(script);
    script.onload = () => {
      console.log("-----googleskd 加载完成");
    };
  }
  //Facebook 登录初始化
  public facebook_init() {
    let d = document,
      s = "script",
      id = "facebook-jssdk";
    var js,
      fjs = d.getElementsByTagName(s)[0];
    if (d.getElementById(id)) {
      console.log("-----Facebookskd 已经加载过");
      return;
    }
    js = d.createElement(s);
    js.id = id;
    js.async = true;
    js.src = "https://connect.facebook.net/en_US/sdk.js";
    fjs.parentNode.insertBefore(js, fjs);
    js.onload = () => {
      console.log("-----Facebookskd 加载完成");
      //FB
      window["fbAsyncInit"] = function () {
        window["FB"].init({
          // appId: '****************',//pt id 无法使用
          appId: "***************", //nustar lemoon id
          xfbml: true,
          version: "v23.0",
        });
      };
    };
  }
  //Google登录
  public google_login() {
    const googleSDK = window["google"];
    if (!googleSDK) {
      showToast("The service is not available in the current region");
      return;
    }
    showZLoading({ showCloseButton: true });
    const client = googleSDK.accounts.oauth2.initCodeClient({
      // client_id: "************-pkhqbtg7m0vd9sap3snpcko78iaomvgj.apps.googleusercontent.com",
      client_id: "************-0l746dl5ve9v6710msbofis0n46qfm76.apps.googleusercontent.com",
      scope:
        "https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email",
      ux_mode: "popup",
      redirect_uri: this.get_redirect_uri(),
      // scope: 'https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email',
      callback: this.handleOuthResponse,
      intermediate_iframe_close_callback: this.logBeforeClose,
    });
    client.requestCode();
  }
  //google logout
  public google_logout() {
    const googleSDK = window["google"];
    if (!googleSDK) return;
    googleSDK.accounts.id.disableAutoSelect();
  }
  logBeforeClose() {
    console.log("------这里登录页面关闭");
  }
  handleOuthResponse(response) {
    closeZLoading();
    console.log("handleCredentialResponse", response);
    if (response.error) {
    } else {
      useLoginStore().handleGoogleLogin(response.code);
    }
  }

  //Facebook 登录
  facebook_login() {
    const FB = window["FB"];
    if (typeof FB === "undefined") {
      showToast("The service is not available in the current region");
      return;
    }
    showZLoading({ showCloseButton: true });
    FB.getLoginStatus(function (response) {
      if (response.status == "connected") {
        let authResponse = response.authResponse;
        useLoginStore().handleFacebookLogin(authResponse.accessToken, authResponse.userID);
        closeZLoading();
      } else {
        if (response.status == "unknown") {
          FB.logout();
          closeZLoading();
        }
        FB.login(function (response) {
          if (response.authResponse) {
            console.log("FaceBookLogin ==>", response);
            let authResponse = response.authResponse;
            useLoginStore().handleFacebookLogin(authResponse.accessToken, authResponse.userID);
          } else {
            console.log("User cancelled login or did not fully authorize.");
            showToast("User cancelled login or did not fully authorize.");
          }
          closeZLoading();
        });
      }
    });
  }

  //Facebook 登出
  facebook_logout() {
    if (typeof window["FB"] === "undefined") {
      return;
    }
    window["FB"].logout();
  }
}
