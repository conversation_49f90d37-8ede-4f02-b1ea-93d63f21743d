export const BTN_SAFE_TIME = 0.1;
export const UI_HIDE_TIME = 0.1;
export const UI_SHOW_TIME = 0.1;
export const UI_POP_OUT_TIME = 0.2;
export const GOLD_RATIO = 100;

// start update_type 字段字典配置
export const BU_GAME = 1; //游戏结算
export const SOURCE_INVITE_FRIENDS = 36; //邀请人送金币

export const SOURCE_ACTIVITY_FIRST_CHARGE = 109; //首充
export const SOURCE_ACTIVITY_WEEKLY_SIGNIN = 110; //周签到活动
export const SOURCE_ACTIVITY_DAILY_REBATE = 111; //每日投注反水活动
export const SOURCE_ACTIVITY_PAYDAY = 112; //发薪日活动
export const SOURCE_ACTIVITY_SPORT = 113; //体育包赔活动
export const SOURCE_ACTIVITY_MAYA_VIP = 114; //mayavip活动
export const SOURCE_ACTIVITY_REDEEMPROMISE = 117; //提现承诺
/*
 * 账变类型 ；后面如果有新的需求，就在后面继续增加就可以，比如 2,3,4 。。。
 */
export const TYPE_ACTIVITY_BALANCE_CHANGE = 1;
export const TYPE_BALANCE_CHANGE = 4;
export const TYPE_BALANCE_RECHARGE = 5;
/*
 * 有公告消息 ；
 */
export const TYPE_NOTICE = 2;
/*
 * 有新的邮件来了；
 */
export const TYPE_MAIL = 3;

export enum E_BALANCE_UPDATE {
  BU_GAME = 1, //游戏结算
  INIT_BET = 2, // 入场费（底注）
  SOURCE_RECHARGE = 11, // 充值
  SOURCE_RECHARGE_CASHBACK = 12, // 充值赠送
  SOURCE_WITHDRAW = 21, // 提现
  SOURCE_WITHDRAW_FAIL = 22, // 提现失败
  SOURCE_INVITE_FRIENDS = 36, //邀请人送金币
  SOURCE_INVITE_FIVE_FRIENDS = 37, //邀请满5人送金币
  SOURCE_INVITEE_FRIENDS = 38, //被邀请人送金币
  SOURCE_MAIL = 51, // 邮件领取
  SOURCE_ADMIN = 41, // 管理员添加(backend使用)
  SOURCE_BONUS = 62,
  SOURCE_USER_REGISTER = 65, // 注册送金币
  SOURCE_SIGN_IN_CASHBACK = 91, // 每日签到送金币
  SOURCE_BIND_PHONE_CASHBACK = 94, // 绑定手机号
  SOURCE_ACTIVITY_FIRST_CHARGE = 109, //首充
  SOURCE_ACTIVITY_WEEKLY_SIGNIN = 110, //周签到活动
  SOURCE_ACTIVITY_DAILY_REBATE = 111, //每日投注反水活动
  SOURCE_ACTIVITY_PAYDAY = 112, //发薪日活动
  SOURCE_ACTIVITY_SPORT = 113, //体育包赔活动
  SOURCE_ACTIVITY_MAYA_VIP = 114, //mayavip活动
  SOURCE_CURRENT_BALANCE = 9001, //玩家当前可用余额查询

  SOURCE_ADMIN_GAME = 201, // 管理员添加-游戏记录(backend使用)
  SOURCE_ADMIN_RECHARGE = 211, // 管理员添加-充值记录(backend使用)
  SOURCE_ADMIN_WITHDRAW = 221, // 管理员添加-提现记录(backend使用)
  SOURCE_ADMIN_BIND_PHONE_CASHBACK = 294, // 管理员添加-绑定手机号(backend使用)
  SOURCE_ADMIN_USER_REGISTER = 265, // 管理员添加-注册送金币(backend使用)
  SOURCE_ADMIN_ACTIVITY_WEEKLY_SIGNIN = 310, // 管理员添加-周签到活动(backend使用)
  SOURCE_ADMIN_ACTIVITY_DAILY_REBATE = 311, // 管理员添加-每日投注反水活动(backend使用)
  SOURCE_ADMIN_ACTIVITY_PAYDAY = 312, // 管理员添加-发薪日活动(backend使用)
  SOURCE_ADMIN_ACTIVITY_SPORT = 313, // 管理员添加-体育包赔活动(backend使用)
  SOURCE_ADMIN_ACTIVITY_BACKEDN_CHANGE = 314, // 管理员调整-修改反水活动产生的用户金额(backend使用)

  SOURCE_ADMIN_INVITE_FRIENDS = 236, // 管理员添加-邀请人送金币(backend使用)
  SOURCE_ADMIN_INVITE_FIVE_FRIENDS = 237, // 管理员添加-邀请满5人送金币(backend使用)
  SOURCE_ADMIN_INVITEE_FRIENDS = 238, // 管理员添加-被邀请人送金币(backend使用)
  SOURCE_ADMIN_FIRST_DEPOSIT_BONUS = 239, // 管理员添加-First deposit bonus送金币(backend使用)
}

export const MAINTENANCETIPCODE = 102121;
export const LOCK_ACCOUNT = 102008;
export const POPBANNERS = "popBanners_";
export const MAN_MADE_LOGIN = "man_made_login";

export enum UI_INDEX {
  REWARD = 100,
  NEW_USER = 1000,
}
export enum E_SCENE_TYPE {
  LOGIN = 1,
  HALL = 2,
  GAME = 3,
}
//目前就用到两个
export enum E_PAGE_TYPE {
  HALL,
  OTHER,
}
export enum E_GAME_TYPE {
  NO_ROOM_LIST,
  WITH_ROOM_LIST,
  THIRD_GAME_LIST,
  THIRD_GAME,
}

export enum E_THIRD_COMPANY_ID {
  PlayZone,
  JILI,
  PG,
  EVO,
  RTG,
  FC,
  Galaxsys,
  Pinnacle,
  BetConstruct,
  SAGaming,
  PP,
  JDB,
  PM,
}

export const E_THIRD_PROVIDER = {
  JILI: "JILI",
  PG: "PG",
  EVO: "EVO",
  RTG: "Realtime Gaming",
  FC: "FaChai",
  Galaxsys: "Galaxsys",
  Pinnacle: "Pinnacle",
  BetConstruct: "Bet Construct",
  SAGaming: "SA Gaming",
  PP: "Pragmatic Play",
  JDB: "JDB",
  PM: "PM",
};

export enum WITHDRAW_TYPE {
  G_CASH = "Gcash",
  MAYA = "Maya",
}

export enum E_CHANEL_TYPE {
  GOOGLE_PLAY = "GP",
  G_CASH = "Gcash",
  H5 = "H5",
  WEB = "Web",
  MAYA = "Maya",
}
export const METHODS = {
  GCASH: "gcash",
  GCASH_WEB: "gcashwebpay",
  GOOGLE_PAY: "googlepay",
  MAYA_PAY: "mayapay",
  PAYCOOLS: "paycools",
  BANK_CARD: "paycoolsbank",
  MAYA_WEB: "mayawebpay",
};

export const PAY_METHOD = {
  MAYA_WEB: "9",
  GCASH_WEB: "10",
};

export enum E_FUND_TYPE {
  Gcash = 3,
  Maya = 4,
  GrabPay = 5,
  BankCard = 6,
  MAYA_MINI = 7,
  MAYA_WEB = 11,
  GCASH_WEB = 12,
}

export enum E_TRANSACTION_TYPE {
  recharge,
  redeem,
}

export enum PRODUCT_TYPE {
  ITEM = 1,
  CASH = 2,
  GCASH = 3,
  MAYA = 4,
  GRAB_PAY = 5,
  BANK_CARD = 6,
  MAYA_MINI = 7,
  MAYA_WEB = 9, //Maya充值
  GCASH_WEB = 10, //GCash充值
  WITHDRAW_MAYA_WEB = 11, //Maya提现
  WITHDRAW_GCASH_WEB = 12, //GCash提现
}
//测试帐号字段type: T-0 测试无 Recharge 和 Withrawal 权限。 T-1 Recharge权限，T-2 Withrawal权限，T-3 Withrawal 和 Recharge
export const E_CAPABILITY_TYPE = {
  NONE: "T-0",
  ONLY_RECHARGE: "T-1",
  ONLY_WITHDRAWAL: "T-2",
  BOTH: "T-3",
};
export const PASS_TYPE = {
  RedeemSuc: "RedeemSuc",
  AddAccount: "AddAccount",
  ResetAccount: "ResetAccount",
  ConfirmPwSuc: "ConfirmPwSuc",
};
export const VERIFY_CODE_TYPE = {
  SetPaymentPwd: "Set Payment Password",
  ChangePaymentPwd: "Change Payment Password",
  SetLoginPwd: "Set Login Password",
  ChangeLoginPwd: "Change Login Password",
  ChangePhoneNum: "Change Phone Number",
  AddWithdrawAccount: "Add Withdraw Account",
};

const TAG_TYPE = {
  DEPOSIT: "deposit",
  WITHDRAW: "withdraw",
  AWARD: "award",
};

const RECORD_TYPE = {
  ADJUSTMENT: "Adjustment",
  MAYAPAY: "mayapay",
  MAYA_WEB: "mayawebpay",
  GCASH_WEB: "gcashwebpay",
  DEPOSIT: "Deposit",
  WITHDRAWAL: "Withdrawal",
  REWARD: "Reward",
  TRANSFER: "Transfer",
};

enum FILTER_DATA {
  TODAY,
  YESTERDAY,
  LAST_THREEDAYS,
  LAST_SEVENTDAYS,
}

enum RECHARGE_STATUS {
  SUCCESS = 1, //成功-1
  PENDING = 2, //等待-2
  FAILURE = 3, //失败-3
  OPERATIONAL = 4, //充值金额不对但是成功回调(人工审核)
}

enum RECHARGE_WEB_STATUS {
  SUCCESS = 1, //成功
  PENDING = 2, //等待
  FAILED = 3, //失败
  CANCEL = 4, //取消
  WAITING = 5, //充值金额不对但是成功回调
  OPERATION = 6,
  WAITING_PAYMENT = 7,
  WAITING_CHANGE_BALANCE = 8,
}

enum WITHDRAW_STATUS {
  PENDING = 1, //待到账
  SUCCESS = 2, //成功
  FAILURE = 3, //失败
  OPERATIONAL = 4, //人工审核(待返回)
  PENDING_APPROVAL = 5, //待审核
}

const RECHAGE_STATUS_DESC = {
  SUCCESS: "Successful",
  PENDING: "Pending",
  FAILED: "Unsuccessful",
  OPERATION: "Operational Handling",
  CANCEL: "Cancel",
  WAITING: "Waiting",
  WAITING_PAYMENT: "Waiting for Payment",
};

const WITHDRAW_STATUS_DESC = {
  PENDING: "Pending",
  SUCCESS: "Successful",
  FAILED: "Unsuccessful",
  OPERATION: "Operational Handling",
};

enum RECORD_TAG {
  DEPOSIT = 1,
  WITHDRAW = 2,
  AWARD = 3,
}

export enum AWARD_UPDATE_TYPE {
  FIRST_RECHARGE = 12,
  FREE_REGISTRATION = 65,
  CASHBACK = 111,
  VIP_CASHBACK_119 = 119, // VIP Cashback //原来是119
  BING_PHONE = 294, // Bind mobile phone
  SIGN_UP_BONUS = 265, // Sign up bonus
  WEEKLY_SIGNIN = 310, // Weekly sign-in
  DAILY_BETTING = 311, // Daily betting rebate
  PAYDAY_BONUS = 312, // Payday bonus
  SPORT_FIRST_TIME = 313, // Sport first time rebate
  INVITE_FRIENDS = 236, // Invite friends
  INVITE_5GIFT = 237, // Invite 5 peoples gift
  INVITEE_GIFT = 238, // Invitee gift
  FIRST_DESPOSIT = 239, // First deposit bonus
  VIP_CASHBACK = 241, // VIP Cashback //原来是119
  SUPERACE_CASH = 242, //
  JILI_GAMES_CASHBACK = 243, //Jili Games cashback
  CASINO_LEADERBOARD = 244, //排行榜奖励
  GET_EASTER_BONUS = 245, //Get Easter Bonus
  YB_SLOT_CASHBACK = 246, //YB Slot Cashback
  SPORTS_LOSS_CASHBACK = 247, //Sports Loss Cashback
  SPORTS_DAILY_BONUS = 248, //Sports Daily Bonus
  NBA_CHAMPION_PREDICTION = 249, //NBA Champion Prediction
  SPIN_ACTIVITY = 400,
  JILI_LEADERBOARD = 401, //jili排行榜奖励
  Valentine = 402, //情人节活动
  WEEKLY_PAYDAY = 403, //WeeklyPayday活动
  LATE_NIGHT_CASHBACK = 404, //Late Night Cashback

  REGISTER_USER = 10000, //新用户注册 奖励30
  BING_IPHONE_USER = 10001, //新用户绑定 原来这三个活动是系统弹窗改变过来
}

export const AWARD_NAME = {
  FREE_REGISTRATION: "Free Registration Bonus",
  CASHBACK: "Daily Cashback",
  BING_PHONE: "Bind mobile phone",
  SIGN_UP_BONUS: "Sign up bonus",
  WEEKLY_SIGNIN: "Weekly sign-in",
  DAILY_BETTING: "Daily Cashback",
  PAYDAY_BONUS: "Payday bonus",
  SPORT_FIRST_TIME: "Sport first time rebate",
  INVITE_FRIENDS: "Invite friends",
  INVITE_5GIFT: "Invite 5 peoples gift",
  INVITEE_GIFT: "Invitee gift",
  FIRST_DESPOSIT: "First deposit bonus",
  VIP_CASHBACK: "VIP Cashback", //原来是119
  SUPERACE_CASH: "SuperAce 1.0% Cashback",
  JILI_GAMES_CASH: "Jili Games 1.0% Cashback",
  CASINO_LEADERBOARD: "Casino Elite Wealth Leaderboard",
  GET_EASTER_BONUS: "Get Easter Bonus",
  YB_SLOT_CASHBACK: "YB Extra 0.35% Cashback",
  SPORTS_LOSS_CASHBACK: "Sports Loss Cashback",
  SPORTS_DAILY_BONUS: "Sports Daily Bonus",
  NBA_CHAMPION_PREDICTION: "NBA Champion Prediction",
  SPIN_ACTIVITY: "Spin Activity",
  JILI_LEADERBOARD: "JILI Slot Rank",
  Valentine: "Valentine",
  WEEKLY_PAYDAY: "Weekly payday",
  LATE_NIGHT_CASHBACK: "Late Night Cashback",

  REGISTER_USER: "Welcome to NUSTAR Online!",
  BING_IPHONE_USER: "Congratulations on successfully\nbinding your mobile phone number",
};

const DATE_TYPE = {
  TODAY: "Today",
  YESTERDAY: "Yesterday",
  LAST_THREEDAYS: "Last 3 days",
  LAST_SEVENTDAYS: "Last 7 days",
};

export enum CHANEL_TERMINAL {
  "GP" = 1,
  "H5" = 2, // otherApp
  "iOS" = 4,
  "Gcash" = 8,
  "Maya" = 64,
  "Web" = 128,
}
