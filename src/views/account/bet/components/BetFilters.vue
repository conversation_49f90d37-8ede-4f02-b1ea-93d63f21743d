<template>
  <div class="header-filter">
    <button :data-key="activeBtn" @click="openDateFilter">
      <span>{{ activeBtn.name }}</span>
      <span class="iconfont icon-xiangxia"></span>
    </button>
    <ZProviderPopup
      v-model:visible="providerVisible"
      :confirm="handleProviderConfirm"
      :checkedProviders="filterProvider"
    >
      <template #default="{ clickFunc }">
        <button @click.stop="() => clickFunc(true)">
          {{ getProviderDisplayText }}
          <span class="provider-len" v-show="filterProvider[0]?.provider">
            +{{ filterProvider.length }}
          </span>
          <span class="iconfont icon-xiangxia"></span>
        </button>
      </template>
    </ZProviderPopup>

    <!-- Date Selection Modal -->
    <ZActionSheet
      v-model="dateVisible"
      title="Select a Date"
      :showConfirmButton="true"
      :showCancelButton="true"
      :onCancel="handleDateCancel"
      :onConfirm="handleDateConfirm"
    >
      <div class="content">
        <van-radio-group v-model="selectedDateOption">
          <van-cell-group inset>
            <van-cell v-for="item in dateOptions" :key="item.value" :title="item.name" clickable>
              <template #right-icon>
                <van-radio :name="item.value">
                  <template #icon="props">
                    <span :class="['check-icon', { checked: props.checked }]">
                      <span class="checked-round"></span>
                    </span>
                  </template>
                </van-radio>
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </ZActionSheet>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useGameStore } from "@/stores/game";
import { storeToRefs } from "pinia";

interface DateOption {
  name: string;
  value: number;
}

interface FilterProvider {
  id: string | number;
  provider: string;
}

interface Props {
  activeBtn: DateOption;
  filterProvider: FilterProvider[];
  dateOptions: DateOption[];
}

interface Emits {
  (e: "update:activeBtn", value: DateOption): void;
  (e: "update:filterProvider", value: FilterProvider[]): void;
  (e: "filter-change"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const gameStore = useGameStore();
const { vaildThirdCompany } = storeToRefs(gameStore);

// Local state
const dateVisible = ref(false);
const providerVisible = ref(false);
const selectedDateOption = ref(props.activeBtn.value);

// Computed properties
const getProviderDisplayText = computed(() => {
  return props.filterProvider[0]?.provider || "Provider";
});

// Date filter handlers
const openDateFilter = () => {
  selectedDateOption.value = props.activeBtn.value;
  dateVisible.value = true;
};

const handleDateConfirm = () => {
  const selectedOption = props.dateOptions.find((d) => d.value === selectedDateOption.value);
  if (selectedOption) {
    emit("update:activeBtn", selectedOption);
    emit("filter-change");
  }
  dateVisible.value = false;
};

const handleDateCancel = () => {
  dateVisible.value = false;
};

// Provider filter handlers
const handleProviderConfirm = (selectedProviders: string[]) => {
  const filteredProviders = vaildThirdCompany.value
    .filter((d: any) => selectedProviders.includes(d.id))
    .map((d: any) => ({
      id: d.id,
      provider: d.provider || d.short_name || "Unknown",
    }));
  emit("update:filterProvider", filteredProviders);
  emit("filter-change");
};
</script>

<style lang="scss" scoped>
.header-filter {
  overflow: hidden;
  display: flex;
  gap: 8px;
  padding: 0 12px;
  top: 55px;
  left: 0;
  position: absolute;
  z-index: 10;

  button {
    color: #666666;
    font-size: 13px;
    font-weight: 500;
    height: 28px;
    padding: 0 14px;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    border-radius: 30px;

    > span {
      padding-left: 10px;
    }

    .provider-len {
      padding: 0 8px;
      color: #fff;
      display: inline-block;
      background-color: #ac1140;
      border-radius: 999px;
      text-align: center;
    }

    img {
      margin-left: 10px;
      vertical-align: sub;
      width: 16px;
      height: 16px;
    }
  }
}

.content {
  width: 100%;

  &:deep(.van-cell-group) {
    margin: 0;
  }

  .check-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid #d0d0d0;
    border-radius: 50%;
    width: 22px;
    height: 22px;

    &.checked {
      border-color: #ac1140;

      .checked-round {
        background: #ac1140;
      }
    }

    .checked-round {
      display: inline-block;
      border-radius: 50%;
      width: 13px;
      height: 13px;
    }
  }
}
</style>
