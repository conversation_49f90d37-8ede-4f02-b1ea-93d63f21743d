<template>
  <van-cell class="items-block">
    <div class="items-title">{{ dateKey }}</div>
    <div class="items-content">
      <div v-for="item in items" :key="item.id">
        <LineSetting 
          valueStatus="normal" 
          :text="item.game_name" 
          :value="item.provider_name" 
          :showArrow="false"
          :rightText="getRightText(item)" 
          :rightStyle="getRightStyle()" 
          @click="handleItemClick(item)"
        >
          <template #icon>
            <img 
              class="item-img" 
              v-lazy="getGameImage(item)" 
              alt="" 
            />
          </template>
        </LineSetting>
      </div>
    </div>
  </van-cell>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import LineSetting from '@/views/account/components/LineSetting.vue';
import PlaceholderBase64 from '@/assets/constants/nsLogoBase64';
import { formatNumberToThousands } from '@/utils/core/tools';

interface BetItem {
  id: string | number;
  game_id: string | number;
  game_name: string;
  provider_name: string;
  win_lose: number;
  bet: number;
  [key: string]: any;
}

interface Props {
  dateKey: string;
  items: BetItem[];
  tabType: string;
  gamesList: Record<string | number, any>;
}

interface Emits {
  (e: 'item-click', item: BetItem): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Style configuration for different tab types
const TAB_STYLES = {
  Settled: { color: "#FF4849", "font-weight": 600 },
  Unsettled: { color: "#909090", "font-weight": 600 },
  Cancel: { color: "#303030", "font-weight": 600 },
  Promos: { color: "#12BE6B", "font-weight": 600 },
} as const;

// Computed properties
const getRightText = (item: BetItem) => {
  let text = formatNumberToThousands(item.win_lose / 100);
  if (props.tabType === "Unsettled") {
    text = "Bet " + formatNumberToThousands(item.bet / 100);
  }
  if (props.tabType === "Promos") {
    text = "+" + text;
  }
  return text;
};

const getRightStyle = () => {
  return TAB_STYLES[props.tabType as keyof typeof TAB_STYLES] || TAB_STYLES.Settled;
};

const getGameImage = (item: BetItem) => {
  return props.gamesList[item.game_id]?.images || PlaceholderBase64;
};

// Event handlers
const handleItemClick = (item: BetItem) => {
  const enrichedItem = {
    ...item,
    images: getGameImage(item),
  };
  emit('item-click', enrichedItem);
};
</script>

<style lang="scss" scoped>
.items-block {
  background: transparent;
  margin-top: 30px;
}

.item-img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

.items-content {
  padding: 16px 12px 0;
  background-color: #fff;
  text-align: left;
  border-radius: 10px;

  &:deep(.line-item) {
    padding: 0 0 14px 0;
  }
}

.items-title {
  color: #6a7a88;
  font-family: Inter;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  border-bottom: 0.5px solid #eee;
  line-height: 48px;
  padding: 0 12px;
  text-align: left;
}
</style>
