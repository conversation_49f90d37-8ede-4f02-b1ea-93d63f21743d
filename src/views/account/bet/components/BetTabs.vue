<template>
  <div class="bet-contents">
    <van-tabs
      line-height="0"
      title-active-color="#fff"
      :active="activeTab"
      swipeable
      @change="handleTabChange"
      @update:active="(val) => $emit('update:activeTab', val)"
    >
      <van-tab
        v-for="(value, key) in betOrders"
        :name="getTabName(key)"
        :key="getTabName(key)"
        :title="key"
      >
        <!-- Empty state -->
        <div v-if="value.count < 1" class="empty-div">
          <ZNoData text="No Record" />
        </div>

        <!-- List with infinite scroll -->
        <van-list
          :loading="loading"
          :finished="value.total_page == value.current_page"
          finished-text=""
          loading-text="loading..."
          :immediate-check="false"
          @load="handleLoadMore(Number(value.current_page) + 1, key)"
          @update:loading="(val) => $emit('update:loading', val)"
        >
          <BetListItem
            v-for="(items, dateKey) in value.list"
            :key="dateKey"
            :dateKey="dateKey"
            :items="items"
            :tabType="key"
            :gamesList="gamesList"
            @item-click="handleItemClick"
          />
        </van-list>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import BetListItem from "./BetListItem.vue";

interface BetOrder {
  count: number;
  total_page: number;
  current_page: number;
  list: Record<string, any[]>;
}

interface Props {
  betOrders: Record<string, BetOrder>;
  activeTab: string;
  loading: boolean;
  gamesList: Record<string | number, any>;
  tabTitles: Record<string, string>;
}

interface Emits {
  (e: "update:activeTab", value: string): void;
  (e: "update:loading", value: boolean): void;
  (e: "load-more", page: number, status: string): void;
  (e: "item-click", item: any): void;
  (e: "tab-change", tab: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Computed properties
const getTabName = (key: string) => {
  return props.tabTitles[key] || key;
};

// Event handlers
const handleTabChange = (name: string | number) => {
  const tabName = String(name);
  emit("update:activeTab", tabName);
  emit("tab-change", tabName);
};

const handleLoadMore = (page: number, status: string) => {
  emit("load-more", page, status);
};

const handleItemClick = (item: any) => {
  emit("item-click", item);
};
</script>

<style lang="scss" scoped>
.bet-contents {
  width: 100vw;
  position: absolute;
  top: 0;
  left: 0;

  // Override tabs styles
  &:deep(.van-tabs) {
    .van-tabs__content {
      height: calc(100vh - 100px);
    }

    .van-tabs__wrap {
      height: 35px;
      padding: 10px;
      box-sizing: content-box;
    }

    .van-tab {
      background: none;
    }

    .van-tabs__nav {
      background-color: #f2f5fc;
      border-radius: 999px;
      line-height: 35px;
      padding: 0;
    }

    .van-tab--active {
      background-color: #ac1140;
      border-radius: 999px;
    }
  }

  &:deep(.van-list) {
    height: 80vh;
    overflow: auto;
  }

  .empty-div {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50vh;
  }
}
</style>
