import { ref, computed } from 'vue';
import { betOrder } from '@/api/user';
import { getGameInfo } from '@/api/games';
import { groupData, formatNumberToThousands } from '@/utils/core/tools';
import PlaceholderBase64 from '@/assets/constants/nsLogoBase64';
import placeholderBannerBase64 from '@/assets/constants/homeBannerBase64';

export interface BetItem {
  id: string | number;
  game_id: string | number;
  game_name: string;
  provider_name: string;
  win_lose: number;
  bet: number;
  third_create_time: string;
  [key: string]: any;
}

export interface BetOrder {
  count: number;
  total_page: number;
  current_page: number;
  list: Record<string, BetItem[]>;
}

export interface FilterProvider {
  id: string | number;
  provider: string;
}

export interface DateOption {
  name: string;
  value: number;
}

export const TAB_TITLES = {
  3: "Settled",
  1: "Unsettled", 
  2: "Cancel",
  promos: "Promos",
  Settled: "3",
  Unsettled: "1",
  Cancel: "2",
  Promos: "promos",
} as const;

export function useBetData() {
  // State
  const loading = ref(false);
  const gamesList = ref<Record<string | number, any>>({});
  const gameIds = ref<(string | number)[]>([]);
  const activeTab = ref<string>("3");
  
  const betOrders = ref<Record<string, BetOrder>>({
    Settled: { count: 0, list: {}, total_page: 0, current_page: 0 },
    Unsettled: { count: 0, list: {}, total_page: 0, current_page: 0 },
    Cancel: { count: 0, list: {}, total_page: 0, current_page: 0 },
    Promos: { count: 0, list: {}, total_page: 0, current_page: 0 },
  });

  const currentPage = ref({
    Settled: 1,
    Unsettled: 1,
    Cancel: 1,
    Promos: 1,
  });

  // Utility functions
  const getRightText = (item: BetItem, tab: string) => {
    let text = formatNumberToThousands(item.win_lose / 100);
    if (tab === "Unsettled") {
      text = "Bet " + formatNumberToThousands(item.bet / 100);
    }
    if (tab === "Promos") {
      text = "+" + text;
    }
    return text;
  };

  const getRightStyle = (tab: string) => {
    const styleMap = {
      Settled: { color: "#FF4849", "font-weight": 600 },
      Unsettled: { color: "#909090", "font-weight": 600 },
      Cancel: { color: "#303030", "font-weight": 600 },
      Promos: { color: "#12BE6B", "font-weight": 600 },
    };
    return styleMap[tab as keyof typeof styleMap] || styleMap.Settled;
  };

  // Batch fetch game details to avoid duplicate requests
  const fetchGameDetails = async (gameIds: (string | number)[]) => {
    const uniqueIds = [...new Set(gameIds)];
    const pendingIds = uniqueIds.filter((id) => !Object.keys(gamesList.value).includes(String(id)));
    if (pendingIds.length === 0) return;

    try {
      const responses = await Promise.all(pendingIds.map((id) => getGameInfo({ id: Number(id) })));
      responses.forEach((res, index) => {
        const gameData = res?.data || res;
        gamesList.value[pendingIds[index]] =
          Array.isArray(gameData) && gameData.length > 0 && gameData.length < 2
            ? gameData[0]
            : { images: PlaceholderBase64 };
      });
    } catch (error) {
      console.error('Failed to fetch game details:', error);
      // Set fallback data for failed requests
      pendingIds.forEach(id => {
        gamesList.value[id] = { images: PlaceholderBase64 };
      });
    }
  };

  // Fetch bet orders with improved error handling
  const fetchBetOrders = async (
    filterProvider: FilterProvider[],
    activeBtn: DateOption,
    data: Record<string, any> = {}
  ) => {
    const providerParam =
      filterProvider.length === 0 || filterProvider.some(p => p.id === "all")
        ? ""
        : filterProvider.map((p) => p.id).join(",");

    const params = {
      provider: providerParam,
      page: 1,
      page_number: 15,
      status: 1,
      date: activeBtn.value,
      ...data,
    };

    const tabKey = params.status;
    currentPage.value[tabKey as keyof typeof currentPage.value] = params.page;

    try {
      const res = await betOrder(params);
      const responseData = res?.data || res;
      const key = TAB_TITLES[tabKey as keyof typeof TAB_TITLES] as string;
      const newList = responseData.list || [];

      if (newList.length > 0) {
        // Extract game IDs and preload game info
        gameIds.value = gameIds.value.concat(newList.map((r: BetItem) => r.game_id));
        
        // Format data
        const formattedList = newList.map((item: BetItem) => ({
          ...item,
          images: placeholderBannerBase64,
          game_name: item.game_name || `${item.provider_name} Promos`,
        }));

        const groupedData = groupData(formattedList, "third_create_time") || {};

        if (params.page === 1) {
          // Initialize or first page data
          betOrders.value[key] = { ...responseData, list: groupedData };
        } else {
          // Merge paginated data
          const currentList = betOrders.value[key].list;
          const timesList = Object.keys(currentList);
          const lastTime = timesList[timesList.length - 1];
          const newTimeList = Object.keys(groupedData);
          const firstTime = newTimeList[0];

          if (lastTime === firstTime) {
            // Same time, merge data
            const { [lastTime]: lastTimeItem, ...others1 } = currentList;
            const { [firstTime]: firstTimeItem, ...others2 } = groupedData;

            betOrders.value[key] = {
              ...responseData,
              list: {
                ...others1,
                [lastTime]: [...lastTimeItem, ...firstTimeItem],
                ...others2,
              },
            };
          } else {
            // Different time, append data
            betOrders.value[key] = {
              ...responseData,
              list: { ...currentList, ...groupedData },
            };
          }
        }
      } else {
        // No data
        betOrders.value[key] = { ...responseData, list: {} };
      }
    } catch (error) {
      console.error("Failed to fetch bet orders:", error);
      throw error;
    }
  };

  // Initialize data for all tabs
  const initData = async (
    filterProvider: FilterProvider[],
    activeBtn: DateOption,
    data: Record<string, any> = {}
  ) => {
    try {
      loading.value = true;
      await Promise.all([
        fetchBetOrders(filterProvider, activeBtn, { status: 3, ...data }),
        fetchBetOrders(filterProvider, activeBtn, { status: 1, ...data }),
        fetchBetOrders(filterProvider, activeBtn, { status: 2, ...data }),
        fetchBetOrders(filterProvider, activeBtn, { status: "promos", ...data }),
      ]);
      await fetchGameDetails(gameIds.value);
    } catch (error) {
      console.error('Failed to initialize data:', error);
    } finally {
      loading.value = false;
    }
  };

  // Load more data for pagination
  const onLoad = async (
    page: number,
    status: string,
    filterProvider: FilterProvider[],
    activeBtn: DateOption
  ) => {
    const currentPageValue = currentPage.value[status as keyof typeof currentPage.value];
    const totalPage = betOrders.value[status]?.total_page || 0;
    
    if (currentPageValue === page || page > totalPage) {
      loading.value = false;
      return;
    }

    try {
      await fetchBetOrders(filterProvider, activeBtn, { 
        page: page || 1, 
        status: activeTab.value 
      });
      await fetchGameDetails(gameIds.value);
    } catch (error) {
      console.error('Failed to load more data:', error);
    } finally {
      loading.value = false;
    }
  };

  return {
    // State
    loading,
    gamesList,
    gameIds,
    activeTab,
    betOrders,
    currentPage,
    
    // Utility functions
    getRightText,
    getRightStyle,
    
    // API functions
    fetchGameDetails,
    fetchBetOrders,
    initData,
    onLoad,
  };
}
