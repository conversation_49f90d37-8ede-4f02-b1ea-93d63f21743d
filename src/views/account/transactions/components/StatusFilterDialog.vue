<template>
  <ZActionSheet
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="Select Status"
    :showCancelButton="false"
    :onConfirm="handleConfirm"
    :onCancel="handleCancel"
  >
    <div class="content">
      <van-radio-group 
        :model-value="selected"
        @update:model-value="$emit('update:selected', $event)"
      >
        <van-cell-group inset>
          <van-cell 
            v-for="option in options" 
            :key="option" 
            :title="option" 
            clickable
          >
            <template #right-icon>
              <van-radio :name="option">
                <template #icon="props">
                  <span :class="['check-icon', { checked: props.checked }]">
                    <span class="checked-round"></span>
                  </span>
                </template>
              </van-radio>
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>
    </div>
  </ZActionSheet>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean;
  selected: string;
  options: readonly string[];
}

defineProps<Props>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'update:selected': [value: string];
  'confirm': [];
  'cancel': [];
}>();

const handleConfirm = () => {
  emit('confirm');
};

const handleCancel = () => {
  emit('cancel');
};
</script>

<style lang="scss" scoped>
.content {
  width: 100%;

  &:deep(.van-cell-group) {
    margin: 0;
  }

  .check-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid #d0d0d0;
    border-radius: 50%;
    width: 22px;
    height: 22px;

    &.checked {
      border-color: #ac1140;

      .checked-round {
        background: #ac1140;
      }
    }

    .checked-round {
      display: inline-block;
      border-radius: 50%;
      width: 13px;
      height: 13px;
    }
  }
}
</style>
