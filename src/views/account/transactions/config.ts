import { formatNumberToThousands } from "@/utils/core/tools";
import { ENUMCONFIGS } from "@/utils/config/Config";

// ==================== 类型定义 ====================
export interface TransactionItem {
  id: string;
  title: string;
  status_name: string;
  right_amount: string;
  payment_method?: string;
  quantity: number;
  amount?: number;
  total_amount?: number;
  recharge_status?: number;
  ship_status?: number;
  audit_status?: number;
  err_msg?: string;
  status_desc?: string;
  update_type?: string;
  created_at: string;
  updated_at?: string;
  pay_channel?: string;
}

export interface PageData {
  list: TransactionItem[];
  total_page: number;
}

export interface TabData {
  [key: string]: TransactionItem[];
}

export interface PageListObj {
  Deposit: TabData;
  Withdrawal: TabData;
  Reward: TabData;
}

export interface CurrentPage {
  Deposit: number;
  Withdrawal: number;
  Reward: number;
}

// ==================== 常量配置 ====================
export const TRANRECORD_TYPE = {
  ADJUSTMENT: "Adjustment",
  MAYAPAY: "mayapay",
  MAYAWEBPAY: "mayawebpay",
  GCASHWEBPAY: "gcashwebpay",
} as const;

export const RECORD_TYPE = {
  DEPOSIT: "Deposit",
  WITHDRAWAL: "Withdrawal",
  REWARD: "Reward",
  TRANSFER: "Transfer",
} as const;

export const TABS = ["Deposit", "Withdrawal", "Reward"] as const;
export type TabType = (typeof TABS)[number];

export const STATUS_MAP = {
  All: "",
  Successful: 1,
  Pending: 2,
  Unsuccessful: 3,
} as const;

export const WITHDRAWAL_STATUS_MAP = {
  All: "",
  Successful: 2,
  Pending: 1,
  Unsuccessful: 3,
} as const;

export const SELECT_STATUS_OPTIONS = ["All", "Pending", "Successful", "Unsuccessful"] as const;
export const RECHARGE_STATUS_OPTIONS = ["All", "Successful", "Pending", "Unsuccessful"] as const;

export const STATUS_COLORS = {
  1: "#11BE6B",
  2: "#FFA500", // 添加待处理状态颜色
  3: "#FF4848",
  Unsuccessful: "#FF4848",
  Successful: "#11BE6B",
  Pending: "#FFA500",
} as const;

// ==================== 页面配置 ====================
export const PAGE_CONFIG = {
  PAGE_SIZE: 15,
  DATE_TYPE: "3",
  INITIAL_PAGE: 1,
  EMPTY_DATA_TEXT: "No Record",
  LOADING_TEXT: "Loading...",
} as const;

export const PAYMENT_METHODS = {
  GCASH: "Gcash",
  MAYA: "Maya",
} as const;

// ==================== 状态映射 ====================
export const DEPOSIT_STATUS_MAP = {
  SUCCESSFUL: "1",
  PENDING: ["2", "5", "6", "7", "8"],
  UNSUCCESSFUL: "3",
} as const;

export const WITHDRAWAL_AUDIT_STATUS = {
  PENDING: ["1", "2", "4", "5"],
  UNSUCCESSFUL: "3",
} as const;

export const WITHDRAWAL_SHIP_STATUS = {
  PENDING: "1",
  SUCCESSFUL: "2",
  UNSUCCESSFUL: "3",
} as const;
// ==================== 工具函数 ====================

/**
 * 合并分组数据，处理分页时相同日期的数据合并
 */
export const mergeGroupedData = (oldData: TabData, newData: TabData): TabData => {
  if (!oldData || !newData) return { ...oldData, ...newData };

  const oldKeys = Object.keys(oldData);
  const newKeys = Object.keys(newData);

  if (oldKeys.length === 0) return newData;
  if (newKeys.length === 0) return oldData;

  const lastOldKey = oldKeys[oldKeys.length - 1];
  const firstNewKey = newKeys[0];

  // 如果最后一个旧日期和第一个新日期相同，合并数据
  if (lastOldKey === firstNewKey) {
    return {
      ...oldData,
      [lastOldKey]: [...(oldData[lastOldKey] || []), ...(newData[firstNewKey] || [])],
      ...Object.fromEntries(newKeys.slice(1).map((k) => [k, newData[k]])),
    };
  }

  return { ...oldData, ...newData };
};

/**
 * 处理支付方式标识
 */
const processPaymentMethod = (paymentMethod?: string): string => {
  if (!paymentMethod) return "";

  const method = paymentMethod.toLowerCase();
  if (method.includes("gcash")) return PAYMENT_METHODS.GCASH;
  if (method.includes("maya")) return PAYMENT_METHODS.MAYA;

  return "";
};

/**
 * 类型守卫：检查是否为有效数字
 */
const isValidNumber = (value: any): value is number => {
  return typeof value === "number" && !isNaN(value) && isFinite(value);
};

/**
 * 格式化金额显示（带错误处理）
 */
const formatAmount = (
  amount: number | undefined,
  totalAmount: number | undefined,
  isWithdrawal: boolean
): string => {
  try {
    const value = isValidNumber(amount) ? amount : isValidNumber(totalAmount) ? totalAmount : 0;
    const absValue = Math.abs(value) / 100;
    const sign = isWithdrawal || value < 0 ? "-" : "+";

    const formattedValue = formatNumberToThousands(absValue);
    return `${sign}${formattedValue}`;
  } catch (error) {
    console.error("Error formatting amount:", error, { amount, totalAmount, isWithdrawal });
    return isWithdrawal ? "-0.00" : "+0.00";
  }
};

/**
 * 处理存款状态（带错误处理）
 */
const processDepositStatus = (item: TransactionItem): void => {
  try {
    const rechargeStatus = String(item.recharge_status ?? "");

    if (rechargeStatus === DEPOSIT_STATUS_MAP.SUCCESSFUL) {
      item.status_name = "Successful";
    } else if (DEPOSIT_STATUS_MAP.PENDING.includes(rechargeStatus)) {
      item.status_name = "Pending";
    } else if (rechargeStatus === DEPOSIT_STATUS_MAP.UNSUCCESSFUL) {
      item.status_name = item.err_msg || "Unsuccessful";
    } else {
      // 默认状态处理
      item.status_name = "Pending";
    }
  } catch (error) {
    console.error("Error processing deposit status:", error, item);
    item.status_name = "Error";
  }
};

/**
 * 处理提款状态（带错误处理）
 */
const processWithdrawalStatus = (item: TransactionItem): void => {
  try {
    item.status_name = item.status_desc || "";
    item.recharge_status = item.ship_status;
    item.amount = item.total_amount;

    const auditStatus = String(item.audit_status ?? "");
    const shipStatus = String(item.ship_status ?? "");

    if (shipStatus === WITHDRAWAL_SHIP_STATUS.UNSUCCESSFUL) {
      item.status_name = item.err_msg || "Unsuccessful";
    } else if (shipStatus === WITHDRAWAL_SHIP_STATUS.SUCCESSFUL) {
      item.status_name = "Successful";
    } else if (shipStatus === WITHDRAWAL_SHIP_STATUS.PENDING) {
      if (WITHDRAWAL_AUDIT_STATUS.PENDING.includes(auditStatus)) {
        item.status_name = "Pending";
      } else if (auditStatus === WITHDRAWAL_AUDIT_STATUS.UNSUCCESSFUL) {
        item.status_name = item.err_msg || "Unsuccessful";
      } else {
        // 默认为待处理状态
        item.status_name = "Pending";
      }
    } else {
      // 未知状态默认为待处理
      item.status_name = "Pending";
    }
  } catch (error) {
    console.error("Error processing withdrawal status:", error, item);
    item.status_name = "Error";
  }
};

/**
 * 处理奖励状态（带错误处理）
 */
const processRewardStatus = (item: TransactionItem, enumList: any[] = []): void => {
  try {
    if (!Array.isArray(enumList)) {
      console.warn("Invalid enumList provided:", enumList);
      enumList = [];
    }

    const enumItem = enumList.find((e) => e?.change_type === item.update_type);
    const configItem = ENUMCONFIGS.find((p) => p?.type === item.update_type);

    item.status_name = enumItem?.title || configItem?.title || "Reward";
  } catch (error) {
    console.error("Error processing reward status:", error, item);
    item.status_name = "Reward";
  }
};

/**
 * 处理交易状态 - 主函数（带错误处理）
 */
export function processTransactionStatus(
  item: TransactionItem,
  tab: TabType,
  enumList: any[] = []
): TransactionItem {
  try {
    // 防御性编程：确保必要字段存在
    if (!item || typeof item !== "object") {
      console.warn("Invalid transaction item:", item);
      return item;
    }

    // 设置基本信息
    item.title = item.pay_channel === TRANRECORD_TYPE.ADJUSTMENT ? "Transfer" : tab;
    item.payment_method = processPaymentMethod(item.payment_method);
    item.right_amount = formatAmount(item.amount, item.total_amount, tab === "Withdrawal");

    // 根据交易类型处理状态
    switch (tab) {
      case "Deposit":
        processDepositStatus(item);
        break;
      case "Withdrawal":
        processWithdrawalStatus(item);
        break;
      case "Reward":
        processRewardStatus(item, enumList);
        break;
      default:
        console.warn(`Unknown transaction tab: ${tab}`);
        item.status_name = "Unknown";
    }

    return item;
  } catch (error) {
    console.error("Error processing transaction status:", error, item);
    // 返回带有默认值的安全对象
    return {
      ...item,
      title: tab,
      status_name: "Error",
      right_amount: "0.00",
      payment_method: "",
    };
  }
}

// ==================== 向后兼容 ====================
// 保持原有的导出名称以避免破坏现有代码
export const tabs = TABS;
