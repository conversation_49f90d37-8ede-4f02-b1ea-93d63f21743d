<template>
  <ZPage
    :request="initializeData"
    backgroundColor="#F4F8FB"
    :narBarStyle="{ background: 'transparent' }"
  >
    <div class="mainbox">
      <!-- 筛选条件 -->
      <TransactionFilter
        v-show="activeTab !== 'Reward'"
        :status="status"
        @filter-click="handleFilterClick"
        @clear-filter="handleClearFilter"
      />

      <div class="bet-contents">
        <!-- 标签页 -->
        <van-tabs
          line-height="0"
          title-active-color="#fff"
          v-model:active="activeTab"
          swipeable
          @change="handleTabChange"
        >
          <van-tab
            v-for="tab in TABS"
            :key="tab"
            :title="tab"
            :name="tab"
            :to="{ name: 'Transactions', params: { tab } }"
            :replace="true"
            title-class="custom-tab"
          >
            <!-- 空状态 -->
            <div v-if="!hasData" class="empty-div">
              <ZNoData :text="PAGE_CONFIG.EMPTY_DATA_TEXT" />
            </div>

            <!-- 列表 -->
            <van-list
              v-else
              v-model:loading="loading"
              :finished="isFinished"
              :loading-text="PAGE_CONFIG.LOADING_TEXT"
              @load="loadMore"
            >
              <TransactionGroup
                v-for="(items, date) in currentTabData"
                :key="date"
                :date="date"
                :items="items"
                :show-top-padding="activeTab !== 'Reward'"
                @item-click="handleItemClick"
              />
            </van-list>
          </van-tab>
        </van-tabs>
      </div>
    </div>

    <!-- 状态筛选弹窗 -->
    <StatusFilterDialog
      v-model="dialogVisible"
      v-model:selected="radioItem"
      :options="SELECT_STATUS_OPTIONS"
      @confirm="handleFilterConfirm"
      @cancel="handleFilterCancel"
    />
  </ZPage>
</template>

<script setup lang="ts">
defineOptions({ name: "Transactions" });

import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useTransactionData } from "./composables/useTransactionData";
import {
  TABS,
  SELECT_STATUS_OPTIONS,
  STATUS_COLORS,
  PAGE_CONFIG,
  type TabType,
  type TransactionItem,
} from "./config";

// 组件导入
import TransactionFilter from "./components/TransactionFilter.vue";
import TransactionGroup from "./components/TransactionGroup.vue";
import StatusFilterDialog from "./components/StatusFilterDialog.vue";

// ==================== 路由和初始化 ====================
const router = useRouter();
const route = useRoute();
const defaultTab = (route.params.tab as TabType) || "Deposit";

// ==================== 使用组合式函数 ====================
const {
  loading,
  activeTab,
  status,
  currentTabData,
  hasData,
  isFinished,
  loadMore,
  switchTab,
  updateStatus,
  initializeData,
} = useTransactionData({ initialTab: defaultTab });

// ==================== 弹窗状态 ====================
const dialogVisible = ref(false);
const radioItem = ref("All");

// ==================== 事件处理函数 ====================

/**
 * 跳转到详情页面
 */
const handleItemClick = (item: TransactionItem) => {
  const isMultiple = item.quantity > 1;
  const path = isMultiple
    ? `/account/transactions/combinelist/${item.id}`
    : `/account/transactions/detail`;
  const query = isMultiple ? {} : item;

  router.push({ path, query });
};

/**
 * 处理标签页切换
 */
const handleTabChange = (tab: TabType) => {
  switchTab(tab);
};

/**
 * 处理筛选按钮点击
 */
const handleFilterClick = () => {
  dialogVisible.value = true;
  radioItem.value = status.value;
};

/**
 * 处理清空筛选
 */
const handleClearFilter = async (e: Event) => {
  e.preventDefault();
  await updateStatus("All");
};

/**
 * 处理筛选确认
 */
const handleFilterConfirm = async () => {
  dialogVisible.value = false;
  await updateStatus(radioItem.value);
};

/**
 * 处理筛选取消
 */
const handleFilterCancel = () => {
  dialogVisible.value = false;
};
</script>
<style lang="scss" scoped>
.custom-tab.van-tab--active {
  background-color: var(--theme-active-tab-color, #ac1140);
}

.content {
  width: 100%;

  &:deep(.van-cell-group) {
    margin: 0;
  }

  .check-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid #d0d0d0;
    border-radius: 50%;
    width: 22px;
    height: 22px;

    &.checked {
      border-color: #ac1140;

      .checked-round {
        background: #ac1140;
      }
    }

    .checked-round {
      display: inline-block;
      border-radius: 50%;
      width: 13px;
      height: 13px;
    }
  }
}

.mainbox {
  position: relative;

  // 覆盖tabs页签样式
  &:deep(.van-tabs) {
    .van-tabs__content {
      height: calc(100vh - 100px);
    }

    .van-tabs__wrap {
      height: 35px;
      padding: 10px;
      box-sizing: content-box;
    }

    .van-tab {
      background: none;
    }

    .van-tabs__nav {
      background-color: #fff;
      border-radius: 999px;
      line-height: 35px;
      padding: 0;
    }

    .van-tab--active {
      background-color: #ac1140;
      border-radius: 999px;
    }
  }

  .bet-contents {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
  }

  .header-filter {
    position: absolute;
    top: 45px;
    left: 0;
    width: 100%;
    display: flex;
    gap: 8px;
    padding: 12px;
    z-index: 3;

    > button {
      background-color: #fff;
      display: flex;
      align-items: center;
      gap: 4px;
      color: #000;
      font-size: 13px;
      font-style: normal;
      font-weight: 500;
      height: 28px;
      padding: 0 14px;
      justify-content: center;
      border-radius: 30px;

      // border: 1px solid #ddd;
      // line-height: 28px;
      > span:last-child {
        color: #999999;
      }

      &.active {
        > span:first-child {
          color: #ac1140;
        }
      }
    }
  }

  .items-block {
    padding: 40px 10px 10px;
  }

  .items-wrap {
    background: #fff;
    border-radius: 10px;
  }

  .items-content {
    padding: 0 12px;

    .icon-img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #f9f9f9;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;

      .item-quantity {
        position: absolute;
        bottom: 0;
        right: -4px;
        padding: 0 6px;
        background: #999;
        color: #fff;
        font-size: 10px;
        border-radius: 12px;
      }
    }

    .gift {
      display: inline-block;
      overflow: hidden;
      width: 28px;
      height: 28px;
      background-image: url(/src/assets/images/account/coin_img.png);
      background-repeat: no-repeat;
      background-size: 100px 55px;
      background-position: left -73px top -2px;

      &.Maya {
        background-position: left -19vw top -7.6vw;
      }

      &.Gcash {
        background-position: left -0.5vw top -2px;
      }
    }
  }

  .items-title {
    color: #6a7a88;
    font-family: Inter;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    border-bottom: 0.5px solid #eee;
    line-height: 48px;
    padding: 0 12px;
  }
}
</style>
