<script setup lang="ts">
import AccountSimpleCard from "./components/AccountSimpleCard.vue";
import type { FormInstance } from "vant";
import { showToast } from "vant";
import { isPhilippinePhoneNumber } from "@/utils/core/tools";
import { addWithdrawAccount, updateWithdrawAccount } from "@/api/withdrawal";
import { rechargeWithdraw } from "@/api/deposit";
import { useGlobalStore } from "@/stores/global";
import { useRoute } from "vue-router";
import router from "@/router";
import { ref, computed, watch } from "vue";
import { setLocalStorage, getLocalStorage } from "@/utils/core/Storage";

// 变量与类型
const route = useRoute();
const globalStore = useGlobalStore();
const isEditMode = computed(() => route.query.type === "edit");
const curUpdateAccountInfo = ref<Record<string, any>>(
  getLocalStorage("withdrawAccountSelected") || {}
);

const formRef = ref<FormInstance>();
const showDialog = ref(false);
const accountList = ref<{ id: number; name: string; account_type: string }[]>([]);
const checkedType = ref<string>("");

const formData = ref({
  accountNo: "",
  firstName: "",
  middleName: "",
  lastName: "",
});

// 获取当前平台名称
const getAccountTypeByName = (name: string) => {
  const res = accountList.value.find((item) => item.name == name);
  return res?.account_type;
};

// 获取当前平台type
const getAccountNameByType = (type: string) => {
  const res = accountList.value.find((item) => item.account_type == type);
  return res?.name;
};

// 提现渠道
const getRechargeWithdraw = async () => {
  const res = await rechargeWithdraw({
    appChannel: globalStore.channel,
  });
  accountList.value = res.withdraw;
  checkedType.value = res.withdraw?.[0]?.name || "";
};

// 选中渠道
const handleCheck = (item: { name: string }) => {
  checkedType.value = item.name;
};

// 校验手机号
const validatePhoneNumber = (value: string) => isPhilippinePhoneNumber(value);

// 格式化手机号
const formatPhoneNumber = (event: Event) => {
  const input = event.target as HTMLInputElement;
  let value = input.value.replace(/\D/g, "");
  console.log("格式化手机号", value, typeof value);
  if (value.length >= 2 && !value.startsWith("09")) {
    value = "09" + value.slice(2);
  }
  value = value.slice(0, 11);
  formData.value.accountNo = value;
};

// 表单提交逻辑
const onSubmit = async () => {
  await formRef.value?.validate();
  showDialog.value = true;
};

const handleConfirm = async () => {
  const { accountNo, firstName, middleName, lastName } = formData.value;
  const params = {
    account_no: accountNo,
    first_name: firstName,
    middle_name: middleName,
    last_name: lastName,
    type: getAccountTypeByName(checkedType.value),
  };
  if (isEditMode.value && curUpdateAccountInfo.value.account_id) {
    await updateWithdrawAccount({
      ...params,
      account_id: curUpdateAccountInfo.value.account_id,
    });
  } else {
    await addWithdrawAccount(params);
  }
  showToast("operate successfully");
  showDialog.value = false;
  router.back();
};

// 按钮可用性
const vaildBtn = computed(
  () => !!(formData.value.accountNo && formData.value.firstName && formData.value.lastName)
);

// 监听编辑模式和渠道列表变化，自动填充表单
watch(
  [() => route.query.type, () => accountList.value],
  ([type, newAccountList]) => {
    if (type === "edit" && newAccountList.length) {
      const Info = curUpdateAccountInfo.value;
      formData.value.firstName = Info.first_name || "";
      formData.value.middleName = Info.middle_name || "";
      formData.value.lastName = Info.last_name || "";
      formData.value.accountNo = Info.account_no || "";
      checkedType.value = getAccountNameByType(Info.type) || "";
    }
  },
  { immediate: true }
);
</script>

<template>
  <ZPage :request="getRechargeWithdraw">
    <div class="content">
      <div class="simple-card">
        <template v-for="item in accountList" :key="item.id">
          <AccountSimpleCard :checkedType="checkedType" @check="handleCheck" :item="item" />
        </template>
      </div>
      <div class="account-form">
        <van-form ref="formRef">
          <div class="form-item">
            <label class="form-label">Maya Account No. (09xx xxxx xxx)</label>
            <van-field maxlength="11" type="digit" @input="formatPhoneNumber" v-model="formData.accountNo"
              name="accountNo" placeholder="Enter your account number" class="form-input" :rules="[
                { required: true, message: 'Account number can not be empty.' },
                { validator: validatePhoneNumber, message: 'Wrong phone number' },
              ]" />
          </div>
          <div class="form-item">
            <label class="form-label">First Name</label>
            <van-field required v-model="formData.firstName" name="firstName" placeholder="Please enter first name"
              :rules="[{ required: true, message: 'First name can not be empty.' }]" class="form-input" />
          </div>
          <div class="form-item">
            <label class="form-label">Middle Name (Optional)</label>
            <van-field v-model="formData.middleName" name="middleName" placeholder="Please enter middle name."
              class="form-input" />
          </div>
          <div class="form-item">
            <label class="form-label">Last Name</label>
            <van-field required v-model="formData.lastName" name="lastName" placeholder="Please enter last name"
              :rules="[{ required: true, message: 'Last name can not be empty.' }]" class="form-input" />
          </div>
        </van-form>
      </div>
    </div>
    <div class="footer">
      <div class="submit-btn">
        <ZButton type="primary" @click="onSubmit" :disabled="!vaildBtn">
          <ZIcon type="icon-duihao" color="#fff"></ZIcon>
        </ZButton>
      </div>
    </div>
    <ZDialog v-model="showDialog" title="Account Confirmation" :onConfirm="handleConfirm" class="dialog">
      <p class="describe">
        Please confirm the {{ checkedType }} account information below is accurate:
      </p>
      <p class="label">
        First Name: <span class="value">{{ formData.firstName }}</span>
      </p>
      <p class="label">
        Middle Name: <span class="value">{{ formData.middleName }}</span>
      </p>
      <p class="label">
        Last Name: <span class="value">{{ formData.lastName }}</span>
      </p>
      <p class="label">
        {{ checkedType }} Account No.: <span class="value">{{ formData.accountNo }}</span>
      </p>
      <p class="tips">
        <ZIcon type="icon-warn" class="tips_icon" color="" :size="14"></ZIcon>
        The platform is not responsible for any losses incurred due to incorrect account information
        provided by the customer.
      </p>
    </ZDialog>
  </ZPage>
</template>

<style lang="scss" scoped>
.content {
  padding: 16px;
  padding-bottom: 120px;
}

.simple-card {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.account-form {
  margin-top: 20px;

  .required {
    color: #ff4d4f;
    margin-right: 4px;
  }

  .form-label {
    margin-bottom: 12px;
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
  }

  .form-input {
    --van-field-border: none;
    /* 移除 Vant Field 默认底部边框 */
    --van-field-padding: 8px 0;
    /* 调整内边距，适配独占一行 */
    background-color: #f7f8fa;
    /* 背景色示例，可根据设计调整 */
    border-radius: 999px;
    display: inline-flex;
    // height: 48px;
    padding: 12px 20px;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
    color: #222;
    font-family: D-DIN;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: -0.3px;
  }
}

.footer {
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  text-align: center;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;

  .submit-btn {
    width: 72px;
    height: 52px;

    .icon-duihao {
      font-size: 28px;
    }
  }
}

.dialog {
  .describe {
    color: #999;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    /* 171.429% */
  }

  .label {
    color: #999;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
    /* 228.571% */
  }

  .value {
    color: #222;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
  }

  .tips {
    margin-top: 12px;
    border-radius: 8px;
    padding: 8px 12px;
    background: var(---, #fffaf8);
    color: #ff936f;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
  }
}
</style>
