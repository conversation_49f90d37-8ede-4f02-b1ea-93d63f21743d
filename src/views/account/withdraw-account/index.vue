<!-- 提现账户列表 -->
<script setup lang="ts">

import AccountCard from "./components/AccountCard.vue";
import { PN_VERIFY_TYPE } from "@/components/ZVerifyDialog/types";
import VerifyDialogPreconditions from "@/components/ZVerifyDialog/VerifyDialogPreconditions.vue";
import VerifyDialogWithdrawAccount from "@/components/ZVerifyDialog/VerifyDialogWithdrawAccount.vue";
import { Status } from "./components/type";
import { showToast } from "vant";
import router from "@/router";
import { setLocalStorage, getLocalStorage } from '@/utils/core/Storage'
import { getWithdrawAccounts } from "@/api/withdrawal";
import { useVerifyPreconditions } from "@/composables/useVerifyPreconditions";

// 列表
const accounts = ref<any[]>([]);
// 总限制数
const totalNum = ref(0);
// 当前数
const currentNum = ref(0);
// 添加、编辑 账户弹窗
const showVerifyDialogWithdrawAccount = ref(false);

// 使用验证前置条件的 composable
const {
  showVerifyDialogPreconditions,
  verifyPreconditions,
  handlePreconditionsConfirm
} = useVerifyPreconditions();

const editWithdrawAccountType = ref(PN_VERIFY_TYPE.AddWithdrawAccount);

const handleVaild = () => {
  // 使用统一的验证方法
  verifyPreconditions(() => {
    showVerifyDialogWithdrawAccount.value = true
  })
};

// 添加账户
const handleAddAccount = () => {
  editWithdrawAccountType.value = PN_VERIFY_TYPE.AddWithdrawAccount;
  //最多可以添加10个提现账号
  if (accounts && accounts.value?.length >= 10) {
    showToast(`Up to ${totalNum} Withdrawal Accounts can be added.`);
    return;
  }
  handleVaild()
};
// 编辑弹窗
const handleEditAccount = (item: any) => {
  editWithdrawAccountType.value = PN_VERIFY_TYPE.ChangeWithdrawAccount;
  setLocalStorage('withdrawAccountSelected', item)
  handleVaild()
};

const handleVerifyCb = () => {
  if (editWithdrawAccountType.value === PN_VERIFY_TYPE.AddWithdrawAccount) {
    router.push(`/account/withdraw-account/edit`);
  } else {
    router.push(`/account/withdraw-account/edit?type=edit`);
  }
};



const getAccounts = async () => {
  const response = await getWithdrawAccounts();
  const { list, current_num, total_num } = response.data || response;
  accounts.value = list;
  currentNum.value = current_num;
  totalNum.value = total_num;
  return {
    list,
    current_num,
    total_num,
  };
};
</script>

<template>
  <ZPage :request="getAccounts">
    <template v-if="accounts.length === 0">
      <ZNoData></ZNoData>
    </template>
    <div class="content" v-else>
      <div class="card-wrap">
        <div v-for="account in accounts" :key="account.id">
          <AccountCard :item="account" :status="Status.EDIT" class="card-item" @click="handleEditAccount">
          </AccountCard>
        </div>
      </div>
    </div>
    <div class="add_btn" @click="handleAddAccount">
      <ZButton style="width: 72px">
        <ZIcon type="icon-jia"></ZIcon>
      </ZButton>
    </div>
    <!-- 绑定手机号、支付密码 -->
    <VerifyDialogPreconditions v-model:showDialog="showVerifyDialogPreconditions"
      :succCallBack="handlePreconditionsConfirm">
    </VerifyDialogPreconditions>
    <!-- 添加、编辑账户 -->
    <VerifyDialogWithdrawAccount v-model:showDialog="showVerifyDialogWithdrawAccount"
      :verifyType="editWithdrawAccountType" :succCallBack="handleVerifyCb" />
  </ZPage>
</template>

<style scoped lang="scss">
.content {
  display: flex;
  flex-direction: column;
  padding: 0 16px;
  padding-bottom: 120px;

  .card-wrap {
    .card-item {
      margin-top: 24px;
    }
  }
}

.add_btn {
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  text-align: center;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}
</style>
