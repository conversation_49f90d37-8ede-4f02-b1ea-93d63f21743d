<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { debounce } from "lodash-es";
import type { CustomNavbarProps, CustomNavbarEmits } from "../types";

// Props 和 Emits
const props = defineProps<CustomNavbarProps>();
const emit = defineEmits<CustomNavbarEmits>();

// 响应式状态
const visible = defineModel("visible", {
  type: Boolean,
  default: false,
});

const router = useRouter();
const searchValue = ref("");
const checkedProviders = ref(["all"]);

// 计算属性
const hasSearchValue = computed(() => searchValue.value.trim().length > 0);

// 实时搜索
const handleSearchInput = () => {
  if (hasSearchValue) {
    handleSearch();
  }
};

const handleSearch = debounce(() => {
  emit("search", searchValue.value.trim());
}, 300);

const handleClearSearch = () => {
  searchValue.value = "";
  emit("search", "");
};

const handleGoBack = () => {
  if (window.history.length > 1) {
    router.back();
  } else {
    router.push("/");
  }
};

const handleConfirmFilters = (selectedProviders: string[]) => {
  checkedProviders.value = selectedProviders;
  visible.value = false;
  props.confirm(selectedProviders);
};

const hasFilters = computed(() => {
  return checkedProviders.value.some((v) => v !== "all");
});

defineExpose({
  setSearchValue: (val: string) => {
    searchValue.value = val || "";
  },
  setCheckedProviders: (val: Array<string>) => {
    checkedProviders.value = val || ["all"];
  },
});
</script>

<template>
  <div class="nav-bar">
    <!-- 返回按钮 -->
    <ZIcon type="icon-fanhui1" @click="handleGoBack" color="" :size="28"></ZIcon>
    <!-- 搜索框 -->
    <van-search
      left-icon=""
      shape="round"
      background="#f4f7fd"
      v-model="searchValue"
      placeholder="Search Games"
      @clear="handleClearSearch"
      @input="handleSearchInput"
    >
      <template #right-icon v-if="!hasSearchValue">
        <ZIcon type="icon-search" @click="handleSearch" color=""></ZIcon>
      </template>
    </van-search>
    <!-- 筛选按钮 -->
    <div class="categories-btn">
      <ZProviderPopup
        v-model:visible="visible"
        :confirm="handleConfirmFilters"
        :checkedProviders="checkedProviders"
      >
        <template #default="{ clickFunc }">
          <ZIcon
            type="icon-shaixuan"
            @click.stop="() => clickFunc(true)"
            :color="hasFilters ? 'red' : ''"
          ></ZIcon>
        </template>
      </ZProviderPopup>
    </div>
  </div>
</template>

<style scoped lang="scss">
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #fff;

  &:deep(.van-search) {
    flex: 1;
    height: 44px;
    margin: 0px 12px;
    border-radius: 999px;

    .van-search__content {
      background-color: #f4f7fd;
      transition: all 0.3s ease;
      border: 1px solid transparent;

      &:focus-within {
        // border-color: var(--primary-color, #007bff);
        // box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
      }
    }

    .van-field__control {
      font-size: 14px;
      color: #333;
    }

    .van-field__control::placeholder {
      color: #999;
    }
  }
}
</style>
