<script setup lang="ts">
defineOptions({ name: "CasinoCate" });

import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  reactive,
  nextTick,
  watch,
  type ComponentPublicInstance,
} from "vue";
import { useRoute } from "vue-router";
import { useCasinoStore } from "@/stores/gameCasino";
import CustomNavbar from "./Components/CustomNavbar.vue";
import { useGameCategoriesStore } from "@/stores/gameCategories";
// 异步组件
import GameItem from "@/components/GameItem.vue";

const gameCategoriesStore = useGameCategoriesStore();
const casinoStore = useCasinoStore();

const route = useRoute();

const navbarRef = ref<null>(null);
const tabRefs = reactive<Record<number, HTMLElement>>({});

// 处理tab切换
const handleTabChange = (index: number) => {
  // 调用store中的方法处理切换逻辑
  casinoStore.handleTabChange(index, tabRefs);
};

// 设置tab的ref，并恢复滚动位置
const setTabRef = (el: Element | ComponentPublicInstance | null, tabId: number) => {
  // casinoStore.setTabRef(el, tabId);
  // if (el && el instanceof HTMLElement) {
  //   tabRefs[tabId] = el;
  // }
};

// 确认筛选（传递给子组件）
const handleConfirmFilters = (categories: string[] = []) => {
  gameCategoriesStore.handleConfirmFilters(categories);
};

// 清除筛选
const handleClearFilters = () => {
  gameCategoriesStore.handleClearFilters();
};

const handleSearch = (value) => {
  gameCategoriesStore.handleSearch(value || "");
};

// 调试：检查无数据显示条件
const shouldShowNoData = (tab: any) => {
  const noData = !tab.pagedGames || !tab.pagedGames.length;
  const notLoading = !casinoStore.isDataLoading;
  return notLoading && noData;
};

// 监听路由变化
watch(
  () => route.query.id,
  (newId, oldId) => {
    if (newId && newId !== oldId) {
      casinoStore.initializeTabFromRoute(newId);
    }
  },
  { immediate: false }
);

const initNavbarData = () => {
  if (navbarRef.value) {
    navbarRef.value.setSearchValue(gameCategoriesStore.filterState.searchValue);
    navbarRef.value.setCheckedProviders(gameCategoriesStore.filterState.selectedCategories);
  }
};

onActivated(() => {
  initNavbarData();
});

// // 监听子组件挂载状态的更可靠方式
watch(
  () => navbarRef.value,
  (navbar) => {
    if (navbar) {
      initNavbarData();
    }
  },
  { immediate: true } // 立即执行一次检查
);

// 生命周期
onMounted(() => {
  // 初始化tab位置并获取数据
  casinoStore.initializeTabFromRoute(route.query.id);
  casinoStore.fetchCasinoGames();
});
</script>

<template>
  <ZPage>
    <div class="categories-container">
      <!-- 导航栏区域 -->
      <div class="nav-bar-effect">
        <CustomNavbar
          ref="navbarRef"
          class="nav-bar"
          :confirm="handleConfirmFilters"
          v-model:visible="casinoStore.dialogVisible"
          @search="handleSearch"
        />
      </div>

      <!-- 主要内容区域 -->
      <div class="categories">
        <div class="categories-tabs">
          <van-tabs
            v-model:active="casinoStore.currentIndex"
            @change="handleTabChange"
            swipeable
            shrink
            line-height="0"
            background="transparent"
          >
            <van-tab v-for="tab in casinoStore.filteredTabs" :key="tab.id" :title="tab.name">
              <div
                class="games-container"
                @scroll="casinoStore.handleScroll($event, tab.id)"
                :ref="(el) => setTabRef(el, tab.id)"
              >
                <!-- 游戏网格 -->
                <van-row
                  class="games-grid"
                  gutter="12"
                  v-if="tab.pagedGames && tab.pagedGames.length > 0"
                >
                  <van-col v-for="game in tab.pagedGames" :key="`${game.id}`" :span="8">
                    <GameItem :game="game as any" @updateLike="casinoStore.updateGameLike" />
                  </van-col>
                </van-row>

                <!-- 无游戏数据状态展示 -->
                <div v-if="shouldShowNoData(tab)" class="no-data">
                  <template v-if="casinoStore.hasFilters">
                    <div>
                      <ZNoData text="Your filters has returned no results"></ZNoData>
                      <div
                        v-if="casinoStore.hasFilters"
                        @click="handleClearFilters"
                        class="clear-filters-btn"
                      >
                        Clear Filters
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <ZNoData text="No Record"></ZNoData>
                  </template>
                </div>
              </div>
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </div>
  </ZPage>
</template>

<style lang="scss" scoped>
.categories-container {
  height: 100%;

  .nav-bar-effect {
    min-height: 60px;

    .nav-bar {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 100;
      backdrop-filter: blur(10px);
      background-color: rgba(255, 255, 255, 0.9);
    }
  }

  .categories {
    height: calc(100vh - 60px);
    overflow: hidden;
    background: linear-gradient(to bottom, #ffffff, #f4f8fb 20%);

    .categories-tabs {
      height: 100%;

      &:deep(.van-tabs) {
        height: 100%;

        .van-tabs__nav {
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(12px);
          position: sticky;
          top: 0;
          z-index: 10;
        }

        .van-tabs__content {
          height: calc(100% - 44px);
        }

        .van-tab__panel {
          height: 100%;
        }
      }

      .games-container {
        height: 100%;
        overflow-y: auto;
        padding: 12px;
        scroll-behavior: auto;
        -webkit-overflow-scrolling: touch;

        .games-grid {
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;

          &:deep(.game-item) {
            margin-bottom: 20px;

            .game-item-img {
              height: 110px !important;
            }

            .game-item-like {
              right: 6px;
              bottom: 6px;
            }
          }
        }

        &::-webkit-scrollbar {
          width: 0;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.2);
        }
      }

      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px 20px;
        text-align: center;

        .clear-filters-btn {
          margin-top: 16px;
          cursor: pointer;
          background-color: #eee;
          display: inline-block;
          padding: 6px 10px;
          border-radius: 20px;
        }
      }
    }
  }
}
</style>
