<template>
  <ZPage>
    <div class="categories-container">
      <!-- 导航栏区域，添加背景效果容器 -->
      <div class="nav-bar-effect">
        <!-- 自定义导航栏组件，传递确认事件、可见性绑定及搜索事件 -->
        <CustomNavbar
          ref="navbarRef"
          class="nav-bar"
          :confirm="handleConfirmFilters"
          v-model:visible="dialogVisible"
          @search="gameCategoriesStore.handleSearch"
        />
      </div>
      <!-- 主要内容区域，分类展示 -->
      <div class="categories">
        <!-- 分类标签页，有分类数据时显示 -->
        <div class="categories-tabs">
          <van-tabs
            v-model:active="currentIndex"
            @change="handleTabChange"
            swipeable
            shrink
            line-height="0"
            background="transparent"
          >
            <van-tab
              v-for="category in filteredCategories"
              :key="category.id"
              :title="category.name"
            >
              <!-- 游戏列表容器，处理滚动事件和ref设置 -->
              <div
                class="games-container"
                @scroll="handleScroll($event, category.id)"
                :ref="(el) => setTabRef(el, category.id)"
              >
                <!-- 特殊分类（casino）的图片展示区域 -->
                <div v-show="isSpecialCategory(category.name)" class="special-category-images">
                  <Casino />
                </div>
                <!-- 游戏数据 -->
                <van-row
                  class="games-grid"
                  gutter="12"
                  v-if="category.pagedGames && category.pagedGames.length > 0"
                >
                  <van-col
                    v-for="game in category.pagedGames"
                    :key="game.id"
                    :span="getGameSpan(game, category)"
                  >
                    <!-- 游戏项组件，非换行标记时渲染，传递游戏数据和更新喜欢状态事件 -->
                    <GameItem
                      v-show="!isLineBreak(game)"
                      :game="game"
                      @updateLike="handleUpdateLike"
                    />
                  </van-col>
                </van-row>
                <!-- 无游戏数据状态展示 - 只有在不加载且确实无数据时显示 -->
                <div v-if="shouldShowNoData(category)" class="no-data">
                  <template v-if="hasFilters">
                    <div>
                      <ZNoData text="Your filters has returned no results"></ZNoData>
                      <div
                        v-if="hasFilters"
                        @click="handleClearFilters"
                        size="small"
                        class="clear-filters-btn"
                      >
                        Clear Filters
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <ZNoData text="No Record"></ZNoData>
                  </template>
                </div>
              </div>
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </div>
  </ZPage>
</template>
<script setup lang="ts">
defineOptions({ name: "Categories" });
import { useRoute, useRouter } from "vue-router";
import { ref, computed, onMounted, onUnmounted, watch, onBeforeMount } from "vue";
import { storeToRefs } from "pinia";

import CustomNavbar from "./Components/CustomNavbar.vue";
import Casino from "./Components/Casino.vue";
import GameItem from "@/components/GameItem.vue";
import { useGameCategoriesStore } from "@/stores/gameCategories";
import { useGameStore } from "@/stores/game";
import type { Game, GameCategory } from "./types";

const route = useRoute();
const gameStore = useGameStore();
const { gameTypes } = storeToRefs(gameStore);

// 使用游戏分类 Store
const gameCategoriesStore = useGameCategoriesStore();
const {
  filteredCategories,
  hasFilters,
  filterState,
  isDataLoading,
  isLoadingMore,
  currentIndex,
  dialogVisible,
} = storeToRefs(gameCategoriesStore);

const navbarRef = ref();

// 计算属性，根据路由参数或默认值获取当前分类ID
const categoryId = computed(() => {
  const id = route.query.categoryId || gameTypes.value[0]?.id || "";
  return String(id);
});

// 处理清除筛选条件操作
const handleClearFilters = () => {
  gameCategoriesStore.handleClearFilters();
  navbarRef.value?.setCheckedProviders();
};

// 处理确认筛选条件操作
const handleConfirmFilters = (categories: string[] = []) => {
  gameCategoriesStore.handleConfirmFilters(categories);
};

// 调试：检查无数据显示条件
const shouldShowNoData = (category: any) => {
  const noData = !category.pagedGames || !category.pagedGames.length;
  const notLoading = !isDataLoading.value;
  const shouldShow = notLoading && noData;
  return shouldShow;
};

// 设置tab的ref（简化版，不记住滚动位置）
const setTabRef = (el: Element | null, categoryId: string | number) => {
  if (el && el instanceof HTMLElement) {
    // 总是从顶部开始，不恢复滚动位置
    // el.scrollTop = 0;
  }
};

// 处理tab切换
const handleTabChange = async (index: number): Promise<void> => {
  await gameCategoriesStore.handleTabChange(index);
};

// 处理滚动事件，距离底部较近时触发加载更多
const handleScroll = async (event: Event, categoryId: string | number): Promise<void> => {
  const element = event.target as HTMLElement;
  const { scrollTop, scrollHeight, clientHeight } = element;

  // 距离底部50px时触发加载更多
  if (scrollHeight - scrollTop - clientHeight < 50) {
    const category = filteredCategories.value.find((c) => c.id === categoryId);
    if (category?.hasMore && !isLoadingMore.value) {
      await gameCategoriesStore.loadMoreGames(categoryId);
    }
  }
};

// 处理更新游戏喜欢状态
const handleUpdateLike = async (updatedGame: Game): Promise<void> => {
  await gameCategoriesStore.handleUpdateLike(updatedGame);
};

// 判断是否为特殊分类（这里特殊分类指'casino'）
const isSpecialCategory = (categoryName: string | number): boolean => {
  return String(categoryName).toLowerCase() === "casino";
};

// 判断是否为换行标记的游戏项
const isLineBreak = (game: Game): boolean => {
  return String(game.id).startsWith("lineBreak_");
};

// 获取游戏项在网格布局中占据的列数
const getGameSpan = (game: Game, cate: GameCategory): number => {
  // like与history列表不区分大小游戏卡片
  return game.big_images_set !== 0 && !["like", "history"].includes(String(cate.id)) ? 12 : 8;
};

// 初始化tab，根据分类ID跳转到对应的tab或默认加载第一个
const initializeTab = async () => {
  if (categoryId.value) {
    // 如果有指定的 categoryId，跳转到对应的 tab
    const index = filteredCategories.value.findIndex((cat) => {
      const catId = String(cat.id);
      const propId = String(categoryId.value);
      return catId === propId;
    });

    if (index !== -1) {
      await handleTabChange(index);
      return;
    }
  }

  // 如果没有指定 categoryId 或找不到对应分类，默认加载第一个
  if (filteredCategories.value.length > 0) {
    await handleTabChange(0);
  }
};

// 监听categoryId变化，切换对应的tab
watch(
  () => categoryId.value,
  async (newId) => {
    if (!newId) return;

    const index = filteredCategories.value.findIndex((cat) => `${cat.id}` === `${newId}`);

    if (index !== -1 && index !== currentIndex.value) {
      await handleTabChange(index);
    }
  }
);

const initNavbarData = () => {
  if (navbarRef.value) {
    navbarRef.value.setSearchValue(gameCategoriesStore.filterState.searchValue);
    navbarRef.value.setCheckedProviders(gameCategoriesStore.filterState.selectedCategories);
  }
};

onActivated(() => {
  initNavbarData();
});

// 挂载前读取路由参数，更新厂商筛选参数
onBeforeMount(() => {
  const providerIds = (() => {
    const queryParam = route.query.providerIds;
    if (!queryParam) return [];
    if (Array.isArray(queryParam)) {
      return queryParam
        .join(",")
        .split(",")
        .filter((id) => id.trim());
    }
    return queryParam.split(",").filter((id) => id.trim());
  })();
  if (providerIds.length > 0) {
    filterState.value.selectedCategories = providerIds;
  }
});

// 组件挂载时初始化tab（如果数据已加载）
onMounted(async () => {
  await initializeTab();
  // if (!filterState.value.selectedCategories.includes("all")) {
  //   navbarRef.value?.setCheckedProviders(filterState.value.selectedCategories);
  // }
});

// 组件卸载时重置状态
onUnmounted(() => {
  gameCategoriesStore.resetState();
});
</script>

<style lang="scss" scoped>
.categories-container {
  height: 100%;

  .nav-bar-effect {
    min-height: 60px;

    .nav-bar {
      // 导航栏固定顶部并添加磨砂玻璃效果
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 100;
      backdrop-filter: blur(10px);
      background-color: rgba(255, 255, 255, 0.9);
    }
  }

  .categories {
    height: calc(100vh - 60px);
    overflow: hidden;
    background: linear-gradient(to bottom, #ffffff, #f4f8fb 20%);

    .categories-tabs {
      height: 100%;

      // 自定义 van-tabs 组件样式
      &:deep(.van-tabs) {
        height: 100%;

        .van-tabs__nav {
          // 优化 tab 导航栏样式
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(12px);
          position: sticky;
          top: 0;
          z-index: 10;
        }

        .van-tabs__content {
          height: calc(100% - 44px);
        }

        .van-tab__panel {
          height: 100%;
        }
      }

      .games-container {
        height: 100%;
        overflow-y: auto;
        padding: 12px;
        -webkit-overflow-scrolling: touch;
        /* 关键属性，启用iOS弹性滚动 */

        .games-grid {
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;

          &:deep(.game-item) {
            margin-bottom: 20px;

            .game-item-img {
              height: 110px !important;
            }

            .game-item-like {
              right: 6px;
              bottom: 6px;
            }
          }
        }

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          // width: 4px;
          width: 0;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 2px;
        }

        // iOS 特定优化
        @supports (-webkit-overflow-scrolling: touch) {
          // 强制启用硬件加速
          -webkit-transform: translate3d(0, 0, 0);
          // 优化滚动回弹
          -webkit-overflow-scrolling: touch;
          // 减少滚动延迟
          -webkit-scroll-snap-type: none;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.2);
        }
      }

      // 特殊分类图片展示区域样式
      .special-category-images {
        margin-bottom: 12px;
      }

      // 无数据状态样式
      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px 20px;
        text-align: center;

        .clear-filters-btn {
          margin-top: 16px;
          cursor: pointer;
          background-color: #eee;
          display: inline-block;
          padding: 6px 10px;
          border-radius: 20px;
        }
      }
    }
  }
}
</style>
