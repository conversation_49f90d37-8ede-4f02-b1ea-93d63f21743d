<!-- 游戏列表组件 -->
<template>
  <section class="games-list">
    <div class="game-menu">
      <!-- 左侧分类列表 -->
      <div class="categories" :class="{ sticky: isSidebarSticky }">
        <div v-for="(category, index) in categories" :key="category.id"
          :class="['category-item', { active: activeCategory === index }]" @click="scrollToCategory(index)">
          <div class="cate-icon">
            <!-- 使用动态组件渲染 SVG -->
            <component :is="activeCategory === index ? category.activeIcon : category.icon" />
          </div>

          <span>{{ category.name }}</span>
        </div>
      </div>

      <!-- 右侧游戏列表 -->
      <div class="games-container" ref="gameContent">
        <div v-for="(category, index) in homeClassifyGames" :key="category.id" :id="'category-' + index"
          class="game-category-section">
          <div class="categories-title" :id="`category-${category.id}`" v-show="category?.games?.length > 0">
            <span>{{ category.name.toUpperCase() }} </span>
            <span class="more-games" @click="handleShowMore(category.id)">
              More Games
              <ZIcon type="icon-qianjin" color=""></ZIcon>
            </span>
          </div>
          <div class="games-grid">
            <van-row gutter="8" justify="start">
              <van-col span="8" v-for="(game, i) in category.games" :key="game.id"
                :ref="i === category.length - 1 ? lastGameRef : undefined">
                <GameItem :game="game" @updateLike="updateLike(index, game, category)" />
              </van-col>
            </van-row>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { useGameStore } from "@/stores/game";
import GameItem from "@/components/GameItem.vue";
import SportsActive from "@/assets/icons/categories/SportsActive.svg";
import BingoActive from "@/assets/icons/categories/BingoActive.svg";
import CasinoActive from "@/assets/icons/categories/CasinoActive.svg";
import ArcadeActive from "@/assets/icons/categories/ArcadeActive.svg";
import PokerActive from "@/assets/icons/categories/PokerActive.svg";
import SlotsActive from "@/assets/icons/categories/SlotsActive.svg";
import Sports from "@/assets/icons/categories/Sports.svg";
import Bingo from "@/assets/icons/categories/Bingo.svg";
import Casino from "@/assets/icons/categories/Casino.svg";
import Poker from "@/assets/icons/categories/Poker.svg";
import Arcade from "@/assets/icons/categories/Arcade.svg";
import Slots from "@/assets/icons/categories/Slots.svg";

interface Props {
  activeCategory: number;
  isSidebarSticky: boolean;
  mainContent: HTMLElement | null;
  topHeight?: number;
}

interface Emits {
  (e: "update:activeCategory", value: number): void;
  (e: "showMore", categoryId: number): void;
  (e: "updateLike", cate: object, game: any, gameIdx: number): void;
}

const CATES_ICONS = {
  Sports: { icon: Sports, activeIcon: SportsActive },
  Bingo: { icon: Bingo, activeIcon: BingoActive },
  Casino: { icon: Casino, activeIcon: CasinoActive },
  Arcade: { icon: Arcade, activeIcon: ArcadeActive },
  Poker: { icon: Poker, activeIcon: PokerActive },
  Slots: { icon: Slots, activeIcon: SlotsActive },
};

const props = withDefaults(defineProps<Props>(), {
  topHeight: 100,
});

const emit = defineEmits<Emits>();

const router = useRouter();
const gameStore = useGameStore();
const { gameTypes, homeClassifyGames, } = storeToRefs(gameStore);

const categories = computed(() => {
  return gameTypes.value.map((cate) => {
    return {
      ...cate,
      ...CATES_ICONS[cate.name],
    };
  });
});


const gameContent = ref<HTMLElement | null>(null);
const lastGameRef = ref();

// 点击分类标题滚动到对应游戏区域
const scrollToCategory = (index: number) => {
  const section = document.getElementById(`category-${index}`);
  if (section && props.mainContent) {
    const offset = section.offsetTop - props.topHeight; // 减去顶部导航高度
    props.mainContent.scrollTo({
      top: offset,
      behavior: "smooth",
    });
  }
};

// 处理显示更多游戏
const handleShowMore = (categoryId: number) => {
  emit("showMore", categoryId);
};

// 处理点赞
const updateLike = (cateIdx: number, game: any) => {
  const index = homeClassifyGames.value[cateIdx].games.findIndex((c) => c.id === game.id);
  if (index > -1) emit("updateLike", cateIdx, game, index);
};
</script>

<style scoped lang="scss">
.games-list {
  margin-top: 10px;
  padding: 0 12px;
}

.game-menu {
  display: flex;
  gap: 12px;
  height: 100%;
  box-sizing: border-box;

  .categories {
    width: 44px;
    background-color: #fff;
    border-radius: 8px;
    height: fit-content;
    padding: 2px;

    &.sticky {
      position: sticky;
      top: 10px;
      /* 顶部导航高度 */
      // z-index: 90;
      width: 44px;
    }

    .category-item {
      padding: 10px 0;
      cursor: pointer;
      text-align: center;
      border-radius: 8px;
      color: #999;
      text-align: center;
      font-family: Inter;
      font-size: 11px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;

      &.active {
        color: #222;
        background: #fbf3f5;
      }

      .cate-icon {
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }
  }

  .games-container {
    flex: 1;
    scroll-behavior: smooth;
    scroll-padding-top: 16px;

    .categories-title {
      // background: rgb(248, 248, 248);
      padding: 8px 0;
      z-index: 2;

      >span:first-child {
        font-size: 18px;
        font-weight: 700;
      }
    }

    .more-games {
      float: right;
      font-weight: 500;
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;

      .icon-qianjin {
        font-size: 10px;
      }
    }

    .games-grid {
      /*  display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px; */

      &:deep(.game-item) {
        margin-bottom: 4px;

        .game-item-img {
          height: 91px !important;
        }

        .game-item-name {
          height: 40px;
          font-size: 10px;
        }
      }

      .show-more {
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        z-index: 10;
        position: relative;
      }


    }
  }
}
</style>
