<!-- 提供商Logo展示组件 -->
<template>
  <div class="about-us">
    <van-row gutter="12">
      <van-col span="6">
        <div class="img-logo">
          <GcashSvg />
        </div>
      </van-col>
      <van-col span="6">
        <div class="img-logo">
          <MayaSvg />
        </div>
      </van-col>
      <van-col span="6" v-for="img in vaildThirdCompany" :key="img.id">
        <template v-if="img.url && !img.imageError">
          <van-image :src="img.url" @load="img.imageError = false" @error="img.imageError = true"
            :loading="img.short_name || img.provider" />
        </template>
        <div class="img-logo" v-if="img.imageError">
          {{ img.short_name || img.provider }}
        </div>
      </van-col>
    </van-row>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { useGameStore } from "@/stores/game";
import GcashSvg from "@/assets/icons/home-provider/gcash.svg";
import MayaSvg from "@/assets/icons/home-provider/maya.svg";
import { getServerSideImageUrl } from "@/utils/core/tools";

const gameStore = useGameStore();
const { vaildThirdCompany } = storeToRefs(gameStore);
</script>

<style scoped lang="scss">
.about-us {
  background-image: url("@/assets/images/home/<USER>");
  background-position: left -10px top 16px;
  background-repeat: no-repeat;
  background-size: 356px 39px;
  width: 100%;
  padding: 65px 20px 12px 12px;

  .img-logo {
    height: 40px;
    line-height: 40px;
    width: 100%;
    margin: 6px;
    background-color: #f9f9f9;
    text-align: center;
  }

  &:deep(.van-row) {
    .van-image {
      height: 40px;
      width: 100%;
      margin: 6px;
      background-color: #f9f9f9;
      text-align: center;
    }
  }
}
</style>
