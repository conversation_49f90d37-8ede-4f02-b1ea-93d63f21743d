<script setup lang="ts">

import { useKycStore } from '@/stores/kyc'
import { S_MEDIA_TYPE } from '@/views/kyc/CONSTANT'

const kycStore = useKycStore()

const { detailFormData, } = storeToRefs(kycStore)

const handleCountryConfirm = (e) => {
  detailFormData.value.account_no = ''
  kycStore.handleSelectConfirm('account_type', e)
}

const updateStore = (field: keyof typeof detailFormData.value, value: any) => {
  kycStore.updateDetailFormData(field, value);
};

</script>
<template>
  <div class="form">
    <div class="form-item">
      <label>Please Select Your Social Media Type</label>
      <ZSelect title="Select Account Type" :modelValue="detailFormData.account_type" :selectList="S_MEDIA_TYPE"
        @confirm="handleCountryConfirm" placeholder="Please select your account type
"></ZSelect>
    </div>
    <div class="form-item" v-if="detailFormData.account_type">
      <label for="account_no">Please Enter Your Social Media Account</label>
      <input v-model="detailFormData.account_no" id="account_no" placeholder="Please enter your account"
        @input="updateStore('account_no', $event.target.value)" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.form {
  .photo-wrap {
    display: flex;
    height: 160px;
    width: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    border-radius: 20px;
    background: #F4F7FD;
  }

  .tips {
    .title {
      color: #222;
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-top: 10px;
      margin-bottom: 10px;
    }

    .desc {
      color: #999;
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      /* 166.667% */
    }
  }

  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    width: 100%;

    .error {
      color: #FF936F
    }

    .same-address {
      margin-top: 4px;
    }

    label {
      margin-bottom: 8px;
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    input {
      --van-field-border: none;
      /* 移除 Vant Field 默认底部边框 */
      --van-field-padding: 8px 0;
      /* 调整内边距，适配独占一行 */
      background-color: #f7f8fa;
      /* 背景色示例，可根据设计调整 */
      border-radius: 999px;
      display: inline-flex;
      height: 42px;
      box-sizing: border-box;
      padding: 12px 20px;
      align-items: center;
      flex-shrink: 0;
      color: #222;
      font-family: D-DIN;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.3px;

      &::placeholder {
        color: #C0C0C0;

        /* 输入框内默认字体 */
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }

    .dob-wrapper {
      width: 100%;
      display: flex;
      gap: 10px;

      input {
        flex: 1;
        width: 33%;
      }
    }
  }
}
</style>
