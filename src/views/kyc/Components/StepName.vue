<script setup lang="ts">

import { useKycStore } from '@/stores/kyc'
import { BRANCH_ENUM, NATIONALLITY_ENUM } from '@/views/kyc/CONSTANT'

const kycStore = useKycStore()

const { detailFormData, detailDayInputErrTip, } = storeToRefs(kycStore)

const updateStore = (field: keyof typeof detailFormData.value, value: any) => {
  kycStore.updateDetailFormData(field, value);
};

</script>
<template>
  <div class="form">
    <div class="form-item">
      <label for="first_name">First Name</label>
      <input v-model="detailFormData.first_name" id="first_name" placeholder="Pleasant enter your first name"
        @input="updateStore('first_name', $event.target.value)" />
    </div>
    <div class="form-item">
      <label for="middle_name">Middle Name (Optional)</label>
      <input v-model="detailFormData.middle_name" id="middle_name" placeholder="Pleasant enter your middle name"
        @input="updateStore('middle_name', $event.target.value)" />
    </div>
    <div class="form-item">
      <label for="middle_name">Last Name</label>
      <input v-model="detailFormData.last_name" id="middle_name" placeholder="Pleasant enter your last name"
        @input="updateStore('last_name', $event.target.value)" />
    </div>
    <div class="form-item">
      <label>Date of Birth</label>
      <div class="dob-wrapper">
        <input v-model="detailFormData.day" placeholder="Day" @input="updateStore('day', $event.target.value)"
          type="number" />
        <input v-model="detailFormData.month" placeholder="Month" @input="updateStore('month', $event.target.value)"
          type="number" />
        <input v-model="detailFormData.year" placeholder="Year" @input="updateStore('year', $event.target.value)"
          type="number" />
      </div>
      <p class="error" v-if="detailDayInputErrTip">{{ detailDayInputErrTip }}</p>
    </div>
    <div class="form-item">
      <label for="middle_name">Branch</label>
      <ZSelect title="Branch" :modelValue="detailFormData.branch" :selectList="BRANCH_ENUM"
        @confirm="(e) => kycStore.handleSelectConfirm('branch', e)" placeholder="Please select your branch"></ZSelect>
    </div>
    <div class="form-item">
      <label for="middle_name">Nationality</label>
      <ZSelect title="Nationality" :fieldNames="{ label: 'country', value: 'country' }"
        :modelValue="detailFormData.nationality" :selectList="NATIONALLITY_ENUM"
        @confirm="(e) => kycStore.handleSelectConfirm('nationality', e)" placeholder="Please select your nationality">
      </ZSelect>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.form {
  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    width: 100%;

    .error {
      color: #FF936F;
      margin-top: 4px;
    }

    label {
      margin-bottom: 8px;
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    input {
      --van-field-border: none;
      /* 移除 Vant Field 默认底部边框 */
      --van-field-padding: 8px 0;
      /* 调整内边距，适配独占一行 */
      background-color: #f7f8fa;
      /* 背景色示例，可根据设计调整 */
      border-radius: 999px;
      display: inline-flex;
      height: 42px;
      box-sizing: border-box;
      padding: 12px 20px;
      align-items: center;
      flex-shrink: 0;
      color: #222;
      font-family: D-DIN;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.3px;

      &::placeholder {
        color: #C0C0C0;

        /* 输入框内默认字体 */
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }

    .dob-wrapper {
      width: 100%;
      display: flex;
      gap: 10px;

      input {
        flex: 1;
        width: 33%;
      }
    }
  }
}
</style>
