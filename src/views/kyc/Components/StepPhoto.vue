<script setup lang="ts">

import { useKycStore } from '@/stores/kyc'
import { NATIONALLITY_ENUM, S_TYPE_ID } from '@/views/kyc/CONSTANT'
import Photograph from '@/views/kyc/Components/Photograph/index.vue'

const kycStore = useKycStore()

const { detailFormData, detailPhotoBase64 } = storeToRefs(kycStore)
const showCamera = ref(false)

const handleCountryConfirm = (e) => {
  detailFormData.value.id_type = ''
  kycStore.handleSelectConfirm('country', e)
}

const getIdTypeEnum = computed(() => {
  if (detailFormData.value.country != 'Philippines') {
    return ['Passports', 'ACR']
  }
  return S_TYPE_ID
})

</script>
<template>
  <div class="form">
    <div class="form-item">
      <label for="country">Country / Region of Document lssuance</label>
      <ZSelect title="Country" :modelValue="detailFormData.country" :selectList="NATIONALLITY_ENUM"
        :fieldNames="{ label: 'country', value: 'country' }" @confirm="handleCountryConfirm" placeholder="Philippines">
      </ZSelect>
    </div>
    <div class="form-item">
      <label for="id_type">Select Type of ID</label>
      <ZSelect title="Select Type of ID" :modelValue="detailFormData.id_type" :selectList="getIdTypeEnum"
        @confirm="(e) => kycStore.handleSelectConfirm('id_type', e)" placeholder="Please select your type of ID">
      </ZSelect>
    </div>
    <div class="form-item">
      <label>Upload ID Photo</label>
      <div class="photo-container" v-if="detailPhotoBase64" @click="showCamera = true">
        <img class="photo-img" :src="detailPhotoBase64" />
      </div>
      <div v-else class="photo-upload" @click="showCamera = true">
        <ZIcon type="icon-xiangji" color="#999" :size="32"></ZIcon>
      </div>
      <div class="tips">
        <div class="title">
          Please upload a photo of your identification card.
        </div>
        <div class="desc">
          <p>
            · Please upload a photo of your identification card.
          </p>
          <p>
            · Provide a clear, complete photo of the ID.
          </p>
          <p>
            · Avoid reflections, shadows, and excessive brightness.
          </p>
          <p>
            · Ensure no part of the ID is covered.
          </p>
          <p>
            · Place the ID on a flat surface when taking the photo.
          </p>
          <p>· Use a valid and undamaged ID Only valid IDs from
            21+ are accepte</p>
        </div>
      </div>
    </div>
    <Photograph v-if="showCamera" @close="showCamera = false" @confirm="kycStore.updateDetailPhotoBase64"></Photograph>
  </div>
</template>

<style lang="scss" scoped>
.form {
  .photo-container {
    width: 330px;
    height: 160px;
    overflow: hidden;
    position: relative;
    margin: 0 auto;
    border-radius: 20px;

    .photo-img {
      position: absolute;
      width: 160px;
      height: 330px;
      transform-origin: 0 0;
      transform: rotate(90deg) translateY(-100%);
      object-fit: fill;
    }
  }

  .photo-upload {
    display: flex;
    height: 160px;
    width: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    border-radius: 20px;
    background: #F4F7FD;
  }

  .tips {
    .title {
      color: #222;
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-top: 10px;
      margin-bottom: 10px;
    }

    .desc {
      color: #999;
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      /* 166.667% */
    }
  }

  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    width: 100%;

    .error {
      color: #FF936F
    }

    .same-address {
      margin-top: 4px;
    }

    label {
      margin-bottom: 8px;
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    input {
      --van-field-border: none;
      /* 移除 Vant Field 默认底部边框 */
      --van-field-padding: 8px 0;
      /* 调整内边距，适配独占一行 */
      background-color: #f7f8fa;
      /* 背景色示例，可根据设计调整 */
      border-radius: 999px;
      display: inline-flex;
      height: 42px;
      box-sizing: border-box;
      padding: 12px 20px;
      align-items: center;
      flex-shrink: 0;
      color: #222;
      font-family: D-DIN;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.3px;

      &::placeholder {
        color: #C0C0C0;

        /* 输入框内默认字体 */
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }

    .dob-wrapper {
      width: 100%;
      display: flex;
      gap: 10px;

      input {
        flex: 1;
        width: 33%;
      }
    }
  }
}
</style>
