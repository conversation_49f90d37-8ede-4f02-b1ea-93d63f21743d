<!-- filepath: /src/views/kyc/detail-form.vue -->
<script setup lang="ts">
import { computed } from 'vue'
import { useKycStore } from '@/stores/kyc'
import { KYC_STEP } from '@/views/kyc/CONSTANT'
import StepName from '@/views/kyc/Components/StepName.vue'
import StepAddress from '@/views/kyc/Components/StepAddress.vue'
import StepPhoto from '@/views/kyc/Components/StepPhoto.vue'
import StepMedia from '@/views/kyc/Components/StepMedia.vue'

const kycStore = useKycStore()
const { detailFormData, detailDayInputErrTip, curStep, isGovemmentOfficial } = storeToRefs(kycStore)

function getStepInfo() {
  switch (curStep.value) {
    case KYC_STEP.KYC_STEP_NAME:
      return {
        stepTitle: 'KYC Verification',
        btnTitle: 'Continue',
        btnDisabled: !(
          detailFormData.value.first_name &&
          detailFormData.value.last_name &&
          detailFormData.value.day &&
          detailFormData.value.month &&
          detailFormData.value.year &&
          detailFormData.value.branch &&
          detailFormData.value.nationality &&
          !detailDayInputErrTip.value
        )
      }
    case KYC_STEP.KYC_STEP_ADDRESS:
      return {
        stepTitle: 'KYC Verification',
        btnTitle: 'Continue',
        btnDisabled: !(
          detailFormData.value.place_of_birth &&
          detailFormData.value.current_address &&
          detailFormData.value.permanent_address &&
          detailFormData.value.work &&
          detailFormData.value.income &&
          isGovemmentOfficial.value
        )
      }
    case KYC_STEP.KYC_STEP_PHOTO:
      return {
        stepTitle: 'Government-Issued ID',
        btnTitle: 'Continue',
        btnDisabled: !(
          detailFormData.value.country &&
          detailFormData.value.id_type
        )
      }
    case KYC_STEP.KYC_STEP_MEDIA:
      return {
        stepTitle: 'Government-Issued ID',
        btnTitle: 'Finish',
        btnDisabled: !(
          detailFormData.value.account_type &&
          detailFormData.value.account_no
        )
      }
    default:
      return {
        stepTitle: '',
        btnTitle: '',
        btnDisabled: true
      }
  }
}
</script>

<template>
  <ZPage :title="getStepInfo().stepTitle" :onBack="kycStore.handlePreStep">
    <div class="content">
      <StepName v-show="curStep === KYC_STEP.KYC_STEP_NAME" />
      <StepAddress v-show="curStep === KYC_STEP.KYC_STEP_ADDRESS" />
      <StepPhoto v-show="curStep === KYC_STEP.KYC_STEP_PHOTO" />
      <StepMedia v-show="curStep === KYC_STEP.KYC_STEP_MEDIA" />
    </div>
    <!-- 提交按钮 -->
    <div class="footer">
      <div class="submit-btn">
        <ZButton type="primary" @click="kycStore.handleNextStep" :disabled="getStepInfo().btnDisabled">
          {{ getStepInfo().btnTitle }}
        </ZButton>
      </div>
    </div>
  </ZPage>
</template>

<style lang="scss" scoped>
.content {
  padding: 16px;
  padding-bottom: 100px;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}

.simple-card {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.form {
  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    width: 100%;

    .error {
      color: #FF936F
    }

    label {
      margin-bottom: 8px;
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    input {
      --van-field-border: none;
      --van-field-padding: 8px 0;
      background-color: #f7f8fa;
      border-radius: 999px;
      display: inline-flex;
      height: 42px;
      box-sizing: border-box;
      padding: 12px 20px;
      align-items: center;
      flex-shrink: 0;
      color: #222;
      font-family: D-DIN;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.3px;

      &::placeholder {
        color: #C0C0C0;
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }

    .dob-wrapper {
      width: 100%;
      display: flex;
      gap: 10px;

      input {
        flex: 1;
        width: 33%;
      }
    }
  }
}

.footer {
  width: 100%;
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;

  .submit-btn {
    padding: 20px;
    width: 100%;
  }
}

.dialog {
  .describe {
    color: #999;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
  }

  .label {
    color: #999;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
  }

  .value {
    color: #222;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
  }

  .tips {
    margin-top: 12px;
    border-radius: 8px;
    padding: 8px 12px;
    background: var(---, #fffaf8);
    color: #ff936f;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;

    .tips_icon {
      font-size: 14px;
    }
  }
}
</style>
