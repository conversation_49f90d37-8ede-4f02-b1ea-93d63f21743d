<script setup lang="ts">
import { useKycStore } from '@/stores/kyc'
import Photograph from '@/views/kyc/Components/Photograph/index.vue'

const kycStore = useKycStore()
const showCamera = ref(false)

const { simpleFormData, simpleDayInputErrTip, simplePhotoBase64 } = storeToRefs(kycStore)

const updateStore = (field: keyof typeof simpleFormData.value, value: any) => {
  kycStore.updateSimpleFormData(field, value);
};

const submitDisabled = computed(() => {
  return !(simpleFormData.value.first_name && simpleFormData.value.last_name && simpleFormData.value.day && simpleFormData.value.month && simpleFormData.value.year && simplePhotoBase64.value && !simpleDayInputErrTip.value)
})

</script>
<template>
  <ZPage :onBack="kycStore.handleSimplePre">
    <div class="content">
      <div class="tip">
        <ZIcon color="#FF936F" type="icon-warn" :size="16"></ZIcon>
        You need to complete the following information before Withdrawal
      </div>
      <div class="form">
        <div class="form-item">
          <label for="first_name">First Name</label>
          <input v-model="simpleFormData.first_name" id="first_name" placeholder="Pleasant enter your first name"
            @input="updateStore('first_name', $event.target.value)" />
        </div>
        <div class="form-item">
          <label for="middle_name">Middle Name (Optional)</label>
          <input v-model="simpleFormData.middle_name" id="middle_name" placeholder="Pleasant enter your middle name"
            @input="updateStore('middle_name', $event.target.value)" />
        </div>
        <div class="form-item">
          <label for="middle_name">Last Name</label>
          <input v-model="simpleFormData.last_name" id="middle_name" placeholder="Pleasant enter your last name"
            @input="updateStore('last_name', $event.target.value)" />
        </div>
        <div class="form-item">
          <label>Date of Birth</label>
          <div class="dob-wrapper">
            <input v-model="simpleFormData.day" placeholder="Day" @input="updateStore('day', $event.target.value)"
              type="number" />
            <input v-model="simpleFormData.month" placeholder="Month" @input="updateStore('month', $event.target.value)"
              type="number" />
            <input v-model="simpleFormData.year" placeholder="Year" @input="updateStore('year', $event.target.value)"
              type="number" />
          </div>
          <p class="error" v-if="simpleDayInputErrTip">{{ simpleDayInputErrTip }}</p>
        </div>
        <div class="form-item">
          <label>Upload ID Photo</label>
          <div class="photo-container" v-if="simplePhotoBase64" @click="showCamera = true">
            <img class="photo-img" :src="simplePhotoBase64" />
          </div>
          <div v-else class="photo-upload" @click="showCamera = true">
            <ZIcon type="icon-xiangji" color="#999" :size="32"></ZIcon>
          </div>
          <div class="tips">
            <div class="title">
              Please upload a photo of your identification card.
            </div>
            <div class="desc">
              <p>
                · Please upload a photo of your identification card.
              </p>
              <p>
                · Provide a clear, complete photo of the ID.
              </p>
              <p>
                · Avoid reflections, shadows, and excessive brightness.
              </p>
              <p>
                · Ensure no part of the ID is covered.
              </p>
              <p>
                · Place the ID on a flat surface when taking the photo.
              </p>
              <p>· Use a valid and undamaged ID Only valid IDs from
                21+ are accepte</p>
            </div>
          </div>
        </div>
        <Photograph v-if="showCamera" @close="showCamera = false" @confirm="kycStore.updateSimplePhotoBase64">
        </Photograph>
      </div>
      <!-- 提交按钮 -->
      <div class="footer">
        <div class="submit-btn">
          <ZButton type="primary" :click="kycStore.handleSimpleSubmit" :disabled="submitDisabled">
            Confirm
          </ZButton>
        </div>
      </div>
    </div>
  </ZPage>
</template>

<style lang="scss" scoped>
.content {
  padding: 16px;
  padding-bottom: 100px;
  background-color: #fff;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}

.tip {
  color: var(--ff-936-f, #FF936F);
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  background-color: #FFFAF8;
  padding: 8px 12px;
  display: flex;
  padding: 8px 12px;
  justify-content: space-between;
  align-items: flex-start;
  border-radius: 8px;
  gap: 6px;
  margin-bottom: 10px;
  /* 150% */
}

.footer {
  width: 100%;
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;

  .submit-btn {
    padding: 20px;
    width: 100%;
  }
}

.photo-container {
  width: 330px;
  height: 160px;
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  border-radius: 20px;

  .photo-img {
    position: absolute;
    width: 160px;
    height: 330px;
    transform-origin: 0 0;
    transform: rotate(90deg) translateY(-100%);
    object-fit: fill;
  }
}

.photo-upload {
  display: flex;
  height: 160px;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  border-radius: 20px;
  background: #F4F7FD;
}

.tips {
  .title {
    color: #222;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .desc {
    color: #999;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
  }
}

.form {
  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    width: 100%;

    .error {
      color: #FF936F;
      margin-top: 4px;
    }

    label {
      margin-bottom: 8px;
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    input {
      --van-field-border: none;
      /* 移除 Vant Field 默认底部边框 */
      --van-field-padding: 8px 0;
      /* 调整内边距，适配独占一行 */
      background-color: #f7f8fa;
      /* 背景色示例，可根据设计调整 */
      border-radius: 999px;
      display: inline-flex;
      height: 42px;
      box-sizing: border-box;
      padding: 12px 20px;
      align-items: center;
      flex-shrink: 0;
      color: #222;
      font-family: D-DIN;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.3px;

      &::placeholder {
        color: #C0C0C0;

        /* 输入框内默认字体 */
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }

    .dob-wrapper {
      width: 100%;
      display: flex;
      gap: 10px;

      input {
        flex: 1;
        width: 33%;
      }
    }
  }
}
</style>
