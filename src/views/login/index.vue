<template>
  <ZPage>
    <div class="login-page">
      <!-- 顶部导航栏 -->
      <LoginHeader :show-close="loginStore.shouldShowCloseButton" :show-back="loginStore.shouldShowBackButton"
        @close="loginStore.handleClosePage" @back="loginStore.handleGoBack" />

      <!-- 主要内容区域 -->
      <div class="login-content">
        <!-- Logo 区域 -->
        <div class="logo-section">
          <Logo class="app-logo" />
        </div>

        <!-- 登录表单区域 -->
        <div class="form-section">
          <!-- 密码登录组件 -->
          <PasswordLogin v-show="loginStore.currentLoginMode === 'password'" :phone-value="loginStore.userPhone"
            :on-login="loginStore.handlePasswordLogin" :on-switch-mode="loginStore.switchToCodeLogin" />

          <!-- 验证码登录组件 -->
          <CodeLogin v-show="loginStore.currentLoginMode === 'code'" :phone-value="loginStore.userPhone"
            :show-code-input="loginStore.isCodeInputMode" :on-login="loginStore.handleCodeLogin"
            :on-switch-mode="loginStore.switchToPasswordLogin" :on-page-change="loginStore.handleCodePageChange"
            :geetest-handler="loginStore.handleGeetestVerification" />
        </div>

        <!-- 第三方登录分割线 -->
        <div v-if="loginStore.hasThirdPartyLogin" class="login-divider">
          <span>OR</span>
        </div>
        <!-- 第三方登录区域 -->
        <ThirdPartyLogin v-if="loginStore.hasThirdPartyLogin" @google-login="loginStore.tapGoogleLogin"
          @facebook-login="loginStore.tapFacebookLogin" />
      </div>

      <!-- 底部区域 -->
      <LoginFooter :show-agreement="!loginStore.isCodeInputMode" :is-agreement-accepted="loginStore.isPrivacyAgreed"
        @agreement-change="loginStore.handleAgreementChange" @navigate="loginStore.handleNavigateToPage" />

      <!-- 隐私政策确认弹窗 -->
      <PrivacyDialog v-model:visible="loginStore.isPrivacyDialogVisible" @confirm="loginStore.handlePrivacyConfirm" />
    </div>
  </ZPage>
</template>

<script setup lang="ts">
/**
 * 登录页面主组件
 * 负责协调各个登录子组件，处理登录流程和状态管理
 */
import { onMounted } from "vue";

// Store
import { useLoginStore } from "@/stores/login";

// 组件
import Logo from "@/assets/RedNuStar.svg";
import PasswordLogin from "./Components/PasswordLogin.vue";
import CodeLogin from "./Components/CodeLogin.vue";
import LoginHeader from "./Components/LoginHeader.vue";
import LoginFooter from "./Components/LoginFooter.vue";
import ThirdPartyLogin from "./Components/ThirdPartyLogin/index.vue";
import PrivacyDialog from "./Components/PrivacyDialog.vue";

// Store 实例
const loginStore = useLoginStore();

// 生命周期钩子
onMounted(() => {
  loginStore.initLoginPage();
});

</script>

<style scoped lang="scss">
/**
 * 登录页面样式
 * 采用垂直布局，头部导航 + 主要内容 + 底部信息
 */
.login-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
}

.logo-section {
  margin-bottom: 20px;

  .app-logo {
    height: 154px;
    width: 154px;
    margin: 0 auto;
  }
}

.form-section {
  width: 100%;
  max-width: 360px;
}

.login-divider {
  margin: 20px auto;
  color: #999;
  display: flex;
  align-items: center;
  font-family: Inter;

  span {
    padding: 0 10px;
    font-size: 12px;
  }

  &::before,
  &::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #F0F0F0;
    width: 40px;
  }
}
</style>
