<template>
  <div class="ranking-podium">
    <div
      v-for="(index, idx) in [1, 0, 2]"
      :key="index"
      :class="`podium-item ${['second', 'first', 'third'][idx]}`"
    >
      <div class="podium-user">
        {{ getMaskedPlayerId(topThreeData[idx]) }}
      </div>
      <div class="podium-amount">
        <IconCoin />
        {{ formatAward(getAward(topThreeData[idx])) }}
      </div>
      <div class="podium-label">{{ betLabel }}</div>
      <div class="podium-bet">
        <IconCoin />
        {{ formatBetAmount(getBetAmount(topThreeData[idx])) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, type PropType } from "vue";
import { maskString } from "@/utils/core/tools";
import { formatAward, formatBetAmount } from "../utils/promoUtils";
import type { RankingItem } from "../composables/useRankingData";

// Define component name for better debugging
defineOptions({
  name: "RankingPodium",
});

interface Props {
  topThreeData: (RankingItem | null)[];
  betLabel?: string;
}

const props = withDefaults(defineProps<Props>(), {
  betLabel: "Bet",
});

// 获取脱敏用户ID
const getMaskedPlayerId = (item: RankingItem | null): string => {
  if (!item?.player_id) return "--";
  return maskString(item.player_id, 2, 3);
};

// 获取奖励金额
const getAward = (item: RankingItem | null): string | number => {
  return item?.award || "--";
};

// 获取投注金额
const getBetAmount = (item: RankingItem | null): string | number => {
  return item?.total_bet_amount || "--";
};
</script>

<style lang="scss" scoped>
.ranking-podium {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  font-family: "D-DIN";
}

.podium-item {
  background: url("@/assets/images/promos/ranking_bg.png") no-repeat;
  background-size: min(75.733vw, 363.518px) min(91.733vw, 440.318px);
  background-position: left -48vw bottom -13.2vw;
  width: 27vw;
  text-align: center;
  padding: 52px 0 10px 0;
  color: #ff9a3c;
  position: relative;

  &.first {
    color: #ffa902;
    width: 33vw;
    height: 54vw;
    z-index: 2;
    background: transparent;
    padding-top: 70px;

    &::before {
      content: "";
      position: absolute;
      left: 50%;
      top: 50.5%;
      width: 200px;
      height: 33vw;
      background: url("@/assets/images/promos/ranking_bg.png") no-repeat;
      background-size: 76vw 92vw;
      background-position: left -6vw bottom -56.2vw;
      transform: translate(-50%, -50%) rotate(-90deg);
      z-index: -1;
    }
  }

  &.second {
    color: #73b5f6;
    height: 176px;
    z-index: 1;
    background-position: left -48vw bottom -7.2vw;
  }

  &.third {
    color: #ff9c59;
    height: 176px;
    z-index: 1;
    background-position: left -20vw bottom -8.2vw;
  }
}

.podium-amount {
  font-size: 18px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.podium-label {
  font-size: 12px;
  color: #bbb;
  margin: 4px 0;
  font-family: "Inter";
}

.podium-user {
  font-size: 14px;
  color: #333;
  display: flex;
  font-weight: 400;
  align-items: center;
  justify-content: center;
  margin: 10px 0 4px;

  /*  &:last-child {
    text-align: left;
    justify-content: start;
  } */
}
.podium-bet {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}
</style>
