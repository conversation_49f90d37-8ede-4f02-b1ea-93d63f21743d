<template>
  <div class="rank-table" :style="tableStyle">
    <!-- Table Header -->
    <van-row class="table-header">
      <van-col v-for="column in columns" :key="column.key" :span="getColumnSpan(column.key)">
        {{ column.label }}
      </van-col>
    </van-row>

    <!-- Table Content -->
    <div class="table-content">
      <van-row
        v-for="(item, idx) in rankingList"
        :key="`${item.player_id}-${item.rank}`"
        class="table-row"
        :class="{
          'is-me': isCurrentUser(item.player_id),
          top3: idx < 3,
        }"
      >
        <!-- Rank Column -->
        <van-col :span="getColumnSpan('rank')" class="rank">
          <span v-if="isCurrentUser(item.player_id)" class="rank-icon me"></span>
          <div v-if="idx < 3" :class="`rank-icon top${idx}`"></div>
          <span v-else class="rank-digital"> {{ item.rank }}</span>
        </van-col>

        <!-- User ID Column -->
        <van-col :span="getColumnSpan('user')" class="user-id">
          {{ getMaskedPlayerId(item.player_id) }}
        </van-col>

        <!-- Bet Amount Column -->
        <van-col :span="getColumnSpan('bet')" class="bet">
          <IconCoin />
          {{ formatBetAmount(item.total_bet_amount) }}
        </van-col>

        <!-- Bonus Column -->
        <van-col :span="getColumnSpan('bonus')" class="bonus">
          <IconCoin />
          {{ formatAward(item.award) }}
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useGlobalStore } from "@/stores/global";
import { maskString } from "@/utils/core/tools";
import { formatAward, formatBetAmount } from "../utils/promoUtils";
import type { RankingItem } from "../composables/useRankingData";
import type { ThemeConfig } from "../configs";

// Define component name for better debugging
defineOptions({
  name: "RankingTable",
});

export interface TableColumn {
  key: string;
  label: string;
  class?: string;
}

interface Props {
  rankingList: RankingItem[];
  columns?: TableColumn[];
  theme?: ThemeConfig;
  customStyle?: Record<string, string>;
}

const COLS_SPAN = {
  rank: 4,
  user: 7,
  bet: 7,
  bonus: 6,
};

const props = withDefaults(defineProps<Props>(), {
  columns: () => [
    { key: "rank", label: "Rank" },
    { key: "user", label: "User ID" },
    { key: "bet", label: "Bet" },
    { key: "bonus", label: "Bonus", class: "rank-bonus" },
  ],
});

const globalStore = useGlobalStore();

// 计算样式
const tableStyle = computed(() => ({
  background: props.theme?.colors.tableBackground || "#ac47ff",
  "--rank-number-color": props.theme?.colors.rankNumber || "#a754ff",
  ...props.customStyle,
}));

// 获取列宽度
const getColumnSpan = (columnKey: string): number => {
  return COLS_SPAN[columnKey as keyof typeof COLS_SPAN] || 6;
};

// 检查是否为当前用户
const isCurrentUser = (playerId: string): boolean => {
  return globalStore.userInfo.user_id === playerId;
};

// 获取脱敏用户ID
const getMaskedPlayerId = (playerId: string): string => {
  return maskString(playerId, 2, 3);
};
</script>

<style lang="scss" scoped>
.rank-table {
  position: relative;
  z-index: 2;
  border-radius: 24px 24px 0 0;
  color: #ffa902;
  box-shadow: 0 -2px 12px rgba(255, 154, 60, 0.08);
  height: calc(100vh - 360px);

  .table-content {
    overflow-y: auto;
    height: calc(100vh - 415px);
    padding-bottom: 55px;
  }
}

.table-header {
  font-weight: 500;
  color: #fff;
  padding: 14px 18px 4px;
  border-radius: 24px 24px 0 0;
  font-size: 13px;
  text-align: center;
  font-family: "Inter";

  > div {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.table-row {
  background: #fff4e5;
  border-radius: 14px;
  margin: 4px 8px;
  font-weight: 700;
  font-size: 15px;
  height: 44px;

  > div {
    padding: 4px 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &.is-me {
    background: #fff4e5;
    border: 2px solid #ffa902;
  }

  .rank {
    color: #a754ff;
    font-size: 16px;
    font-weight: 700;
    position: relative;

    .rank-digital {
      display: inline-block;
      width: 8.6vw;
      text-align: center;
      color: var(--rank-number-color, #a754ff);
    }

    .isMe-icon {
      width: 32px;
      height: auto;
    }

    .rank-medal {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      font-size: 12px;
      font-weight: bold;
      color: #fff;
      background: #ffa902;
    }
  }

  .user-id {
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: "D-DIN";
  }

  .bet {
    color: #333;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 4px;
  }

  .bonus {
    color: #f5b608;
    font-size: 15px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 4px;
  }
}

.rank-icon {
  width: 9vw;
  height: 6.5vw;
  background: url("@/assets/images/promos/ranking_bg.png") no-repeat;
  background-size: 75.733vw 91.733vw;
  background-position: bottom 0 right -28.8vw;

  &.top1 {
    background-position: bottom 0 right -47.2vw;
  }

  &.top2 {
    background-position: bottom 0 right -38vw;
  }

  &.me {
    background-position: bottom 0 right -8vw;
    position: absolute;
    top: -1px;
    left: 2px;
  }
}
</style>
