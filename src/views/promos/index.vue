<script setup lang="ts">
// 定义组件名称
defineOptions({ name: "PromosAndTournament" });

import { ref, watch, computed } from "vue";
import { getTournamentList } from "@/api/promos";
import { sortArray, preloadImages } from "@/utils/core/tools";
import Promos from "./promos.vue";
import Tournament from "./tournament.vue";
import { getServerSideImageUrl } from "@/utils/core/tools";
import { useRoute, useRouter } from "vue-router";
import { tournamentAssets } from "./configs";
import { useGlobalStore } from "@/stores/global";
import { useBannerStore } from "@/stores/banner";

const globalStore = useGlobalStore();
const bannerStore = useBannerStore();
const route = useRoute();
const router = useRouter();
const activeTab = ref(parseInt(route.params.tab as string) || 0);
const tournamentList = ref<any[]>([]);
const loading = ref(false);

// 使用 banner store 的数据和状态
const banners = computed(() => bannerStore.sortedBanners);
const bannersLoading = computed(() => bannerStore.isLoading);

// 监听路由参数变化，更新activeTab
watch(
  () => route.params.tab,
  (newTab) => {
    const tabIndex = parseInt(newTab as string) || 0;
    activeTab.value = tabIndex;
    // 鉴权判断，未登录则返回登录页
    if (tabIndex === 1 && !globalStore.token) {
      router.push("/login");
      return;
    }
  },
  { immediate: true }
);

// 获取promos列表
const getBannersData = async () => {
  try {
    await bannerStore.fetchBanners();
  } catch (error) {
    console.error("获取活动数据失败:", error);
  }
};

// 刷新promos列表
const refreshBannersData = async () => {
  try {
    await bannerStore.refreshBanners();
  } catch (error) {
    console.error("刷新活动数据失败:", error);
  }
};

// 获取竞赛自定义排行榜列表
const getTournamentDatas = async () => {
  if (!globalStore.token) return;
  loading.value = true;
  try {
    const res = await getTournamentList();
    // Handle both direct response and AxiosResponse formats
    const responseData = res.data || res;
    const listData = responseData?.list;

    if (listData && Array.isArray(listData)) {
      tournamentList.value = sortArray(listData, "sort").map((t) => ({
        ...t,
        banner_image: getServerSideImageUrl(t.banner_image),
      }));
      const urls = [...new Set(tournamentList.value.map((t) => t.banner_image))];
      preloadImages([...new Set(urls)].concat(Object.values(tournamentAssets.badges)));
    } else {
      tournamentList.value = [];
    }
  } catch (error) {
    console.error("获取竞赛自定义排行榜列表 error:", error);
    tournamentList.value = [];
  } finally {
    loading.value = false;
  }
};

// 初始化获取两个列表数据
const init = async () => {
  await Promise.all([getBannersData(), getTournamentDatas()]);
};
// 切换tab
const changeTab = (index: number | string) => {
  console.log("切换tab:", index);
  if (index === 1 && !globalStore.token) {
    router.push("/login");
    return;
  }
};
</script>
<template>
  <ZPage :showNarBar="false" :request="init">
    <div class="promos-wrap">
      <van-tabs swipeable sticky line-height="0" v-model:active="activeTab" @change="changeTab">
        <van-tab
          :to="{ name: 'PromosAndTournament', params: { tab: 0 } }"
          :replace="true"
          title="Promos"
          class="tab-wrap"
        >
          <van-pull-refresh
            loading-text="loading..."
            loosing-text="Release to refresh"
            pulling-text="Pulling to refresh"
            success-text="Refresh successful"
            v-model="bannersLoading"
            @refresh="refreshBannersData"
          >
            <Promos :banners="banners" />
          </van-pull-refresh>
        </van-tab>
        <van-tab
          :to="{ name: 'PromosAndTournament', params: { tab: 1 } }"
          :replace="true"
          title="Tournament"
          class="tab-wrap"
        >
          <van-pull-refresh
            loading-text="loading..."
            loosing-text="Release to refresh"
            pulling-text="Pulling to refresh"
            success-text="Refresh successful"
            v-model="loading"
            @refresh="getTournamentDatas"
          >
            <Tournament :tournamentList="tournamentList" />
          </van-pull-refresh>
        </van-tab>
      </van-tabs>
    </div>
  </ZPage>
</template>

<style scoped lang="scss">
.tab-wrap {
  height: calc(100vh - 100px);
  overflow-y: auto;
}

.promos-wrap {
  &:deep(.van-tabs) {
    .van-tabs__wrap {
      background: #fff;

      .van-tabs__nav {
        justify-content: center;
        gap: 12px;
      }

      .van-tab {
        background: #f4f7fd;
        color: #222;
        font-weight: 600;
        height: 40px;
        font-size: 18px;
        width: 40%;
        flex: none;
      }

      .van-tab--active {
        background-color: #ac1140;
        color: #fff;
      }
    }
  }
}

.promos {
  padding: 12px;

  .promos-item {
    width: 100%;
    height: 94px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    border-radius: 20px;
    overflow: hidden;

    img {
      width: 100%;
      height: 94px;
      object-fit: fill;
    }
  }

  .no-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: 40px 20px;
  }
}
</style>
