<template>
  <ZPage>
    <div class="page">
      <div class="top-section">
        <NuStar></NuStar>
        <img class="system_maintenance" src="@/assets/images/system_maintenance.png" />
        <div class="tip">SYSTEM MAINTENANCE</div>
        <div class="message">
          The website is currently being upgraded. If you're looking to play NUSTAR Online, come and
          join us at Maya Lucky Games!
        </div>
        <ZButton class="contact-btn" :click="handleContact">Contact Support</ZButton>
      </div>
      <div class="bottom-section">
        <div></div>
        <div class="footer">
          <img src="@/assets/images/pagcor_logo.png" class="img2" />
          <div class="space"></div>
          <img src="@/assets/images/21_logo.png" class="img1" />
        </div>
      </div>
    </div>
  </ZPage>
</template>
<script setup type="ts">
import NuStar from '@/assets/NuStar.svg'
import { serviceMgr, ServiceType } from '@/utils/ServiceMgr'
import { useGlobalStore } from '@/stores/global'
import { E_CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import { getEnvConfig } from "@/utils/config/Config";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";
import { useRoute } from "vue-router";

// 获取路由query 参数
const route = useRoute()

const globalStore = useGlobalStore()

const handleContact = () => {
  serviceMgr.instance.openChat(ServiceType.Custom)
}
// 暂时没用到
const handleClose = () => {
  if (globalStore.token) {
    globalStore.loginOut(false)
  }
  if (globalStore.channel === E_CHANEL_TYPE.G_CASH) {
    if (true) {
      //直接关闭这个页面
      window.close()
    } else {
      //跳转gcash 小程序
      const success = MobileWindowManager.navigateToUrl(getEnvConfig().VITE_GCASH_SHOP_URL);
      if (!success) {
        console.error("Failed to open GCash URL");
      }
      setTimeout(() => {
        window.close()
      }, 1000)
    }
  }
}
// 联系客服、关闭页面
</script>
<style lang="scss" scoped>
.page {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  height: 100%;
  background-color: #fff;

  .top-section {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 40px;

    .system_maintenance {
      width: 190px;
      height: 128px;
    }

    .tip {
      color: #242424;
      text-align: center;
      font-variant-numeric: lining-nums proportional-nums;
      font-family: Inter;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 32px;
      /* 160% */
    }

    .message {
      margin-top: 10px;
      color: #6a7a88;
      text-align: center;
      font-variant-numeric: lining-nums proportional-nums;
      font-family: Inter;
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      line-height: 23px;
      /* 153.333% */
    }

    .contact-btn {
      margin-top: 16px;
      background: linear-gradient(90deg, #f3d089 0%, #f1cc82 100%);
      color: #432d00;
      text-align: center;
      font-family: Inter;
      font-size: 15px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }

  .bottom-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    padding-bottom: 40px;

    .footer {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      align-self: flex-end;

      .img1 {
        width: 65px;
        height: 22px;
      }

      .img2 {
        width: 86px;
        height: 23px;
      }

      .space {
        margin-left: 10px;
        margin-right: 10px;
        width: 1px;
        height: 12px;
        background-color: #ddd;
      }
    }
  }
}
</style>
