{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["ESNext", "DOM"], "useDefineForClassFields": true, "baseUrl": ".", "module": "ESNext", "moduleResolution": "Node", "paths": {"@/*": ["src/*"], "#/*": ["types/*"]}, "resolveJsonModule": true, "typeRoots": ["./node_modules/@types/", "./types"], "types": ["vite/client"], "strict": true, "noImplicitAny": false, "sourceMap": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "vite.config.ts", "src/utils/JumpGame.ts"], "exclude": ["node_modules", "dist", "**/*.js"]}